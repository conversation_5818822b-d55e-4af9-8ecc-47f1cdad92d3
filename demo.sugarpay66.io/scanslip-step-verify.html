<!doctype html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable">

<head>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="sugarpay66 payment">
    <meta name="keywords" content="sugarpay66 payment">
    <meta name="author" content="member.sugarpay66.io">
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta property="og:image" content="/public/assets//images/logo/android-chrome-512x512.png" />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>


    <style>
        :root {
            --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
            --theme-color_primary:#6e5c60;
        }
    </style>
    
    <!-- For IE6+ -->
    <link rel="shortcut icon" href="public/assets/images/logo/favicon.ico" type="image/x-icon">

    <!-- For all other browsers -->
    <link rel="icon" href="public/assets/images/logo/favicon.ico"/>

    <!-- Different sizes -->
    <link rel="icon" href="public/assets/images/logo/favicon-16x16.png" sizes="16x16">
    <link rel="icon" href="public/assets/images/logo/favicon-32x32.png" sizes="32x32">

    <!-- For Modern Browsers with PNG Support -->
    <link rel="icon" type="image/png" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link rel="icon" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link rel="apple-touch-icon" href="public/assets/images/logo/apple-touch-icon.png" />

    <!-- Favicon without reflective shine -->
    <link rel="apple-touch-icon-precomposed" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- jsvectormap css -->
    <link href="public/assets/libs/jsvectormap/css/jsvectormap.min.css" rel="stylesheet" type="text/css" />

    <!--Swiper slider css-->
    <link href="public/assets/libs/swiper/swiper-bundle.min.css" rel="stylesheet" type="text/css" />


    <!-- Sweet Alert css-->
    <link href="public/assets/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />

    <link href="public/assets/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link href="public/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="public/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="public/assets/css/app.css?t=1746271793" rel="stylesheet" type="text/css" />

    <!-- custom Css-->
    <link href="public/assets/css/custom.css?t=1746271793" rel="stylesheet" type="text/css" />


    <!--datatable css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <!--datatable responsive css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <style type="text/css">
        .text-bold{
            font-weight: bold;
        }
        .text-right{
            text-align: right;
        }
        .modal-xl {
            max-width: 1300px !important;
        }
        #modalAnnouncement img{
            width :100%;
        }
        #modalAnnouncement  p {
            margin-top: 0;
            margin-bottom: 0.2rem;
        }
    </style>
    
<!-- Filepond css -->
<link rel="stylesheet" href="public/assets/libs/filepond/filepond.min.css" type="text/css" />
<link rel="stylesheet" href="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.css">

<style>
     [v-cloak] { display: none; }
    th{
        vertical-align: middle;
    }
    .table>tbody>tr>td{
        vertical-align: middle;
    }
    .highlight{
        color: #000;
        font-weight:bold;
        background-color: rgba(41,156,219,.1);
    }
</style></head>

<body>

<!-- Begin page -->
<div id="layout-wrapper">

    <header id="page-topbar">
        <div class="layout-width">
            <div class="navbar-header">
                <div class="d-flex">
                    <!-- LOGO -->
                    <div class="navbar-brand-box horizontal-logo">
                        <a href="" class="logo logo-dark">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>

                        <a href="index.html" class="logo logo-light">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>
                    </div>

                    <button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                            id="topnav-hamburger-icon">
                            <span class="hamburger-icon">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                    </button>
                </div>

                <div class="d-flex align-items-center">
                                        <div class="ms-1 header-item d-none d-sm-flex">
                        <button type="button" class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                                data-toggle="fullscreen">
                            <i class='bx bx-fullscreen fs-22'></i>
                        </button>
                    </div>
                    <div class="dropdown ms-sm-3 header-item topbar-user">
                        <button type="button" class="btn" id="page-header-user-dropdown" data-bs-toggle="dropdown"
                                aria-haspopup="true" aria-expanded="false">
                                <span class="d-flex align-items-center">
                                    <img class="rounded-circle header-profile-user" src="public/assets/images/logo/logo-sm.png"
                                         alt="Header Avatar">
                                    <span class="text-start ms-xl-2">
                                        <span class="d-none d-xl-inline-block ms-1 fw-medium user-name-text">tiger-001</span>
                                        <span class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"></span>
                                    </span>
                                </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <!-- item-->
                            <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                                                        <a class="dropdown-item" href="login/logout"><i
                                        class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span
                                        class="align-middle" data-key="t-logout">ออกจากระบบ</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- ========== App Menu ========== -->
    <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
            <!-- Dark Logo-->
            <a href="" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <!-- Light Logo-->
            <a href="" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
                <i class="ri-record-circle-line"></i>
            </button>
        </div>

        <div id="scrollbar">
            <div class="container-fluid">

                <div id="two-column-menu">
                </div>
                <ul class="navbar-nav mt-3" id="navbar-nav">
                                            <li class="nav-item">
                            <a class="nav-link menu-link " href="dashboard.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Dashboard</span>
                            </a>
                        </li>
                                        
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="transaction.html" >
                            <i class="ri-article-line"></i> <span data-key="t-users">Transaction</span>
                        </a>
                    </li>
                                                                <li class="nav-item">
                            <a class="nav-link menu-link " href="withdraw_approve.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Withdraw Approve</span>
                            </a>
                        </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="bank_statement.html" >
                            <i class="ri-bank-line"></i> <span data-key="t-users">Statement</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="summary_transfer.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Settlement</span>
                        </a>
                    </li>
                    
                                        <a class="nav-link menu-link " href="#sidebarReport" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarReport">
                        <i class="ri-article-line"></i> <span data-key="t-report">Report</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarReport">
                        <ul class="nav nav-sm flex-column">

                                                        <li class="nav-item">
                                <a href="report_agent_daily_realtime.html" class="nav-link" data-key="t-report">Daily Summary Report</a>
                            </li>
                             

                                                            <li class="nav-item">
                                    <a href="report_withdraw_channel.html" class="nav-link " data-key="t-users">Withdraw Channel Report</a>
                                </li>
                            
                                                        <li class="nav-item">
                                <a href="report_withdraw.html" class="nav-link" data-key="t-report">Withdraw Activity Report</a>
                            </li>
                             

                                                        <li class="nav-item">
                                <a href="report_deposit.html" class="nav-link" data-key="t-report">Deposit Activity Report</a>
                            </li>
                             
                            
                            <!--                            <li class="nav-item">-->
<!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
<!--                            </li>-->
                                                    </ul>
                    </div>
                    
                  

                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="withdraw_fund.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Withdraw Fund</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-import.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 1 Import Slip</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-verify.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 2 Verify Slip</span>
                        </a>
                    </li>
                    

                                        <a class="nav-link menu-link " href="#sidebarTools" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTools">
                        <i class="ri-article-line"></i> <span data-key="t-report">Tools</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarTools">
                        <ul class="nav nav-sm flex-column">
                             
                                                        <li class="nav-item">
                                <a href="black_list.html" class="nav-link" data-key="t-report">Blacklist Manage</a>
                            </li>
                             
 
                        </ul>
                    </div>
                    

                    <li class="nav-item">
                        <a class="nav-link menu-link " href="profile.html" >
                            <i class="ri-folder-user-line"></i> <span data-key="t-users">Profile</span>
                        </a>
                    </li>
                                            <a class="nav-link menu-link " href="#sidebarSetupUser" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSetupUser">
                            <i class="ri-folder-user-line"></i> <span data-key="t-setup-user">Settings</span>
                        </a>
                                                <div class="collapse menu-dropdown " id="sidebarSetupUser">
                            <ul class="nav nav-sm flex-column">
                                <li class="nav-item">
                                    <a href="user_group.html" class="nav-link " data-key="t-user-group"> User Groups </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user_permission.html" class="nav-link " data-key="t-permission"> Group Permission </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user" class="nav-link " data-key="t-users"> Sub Users </a>
                                </li>
                            </ul>
                        </div>
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="login/logout" >
                            <i class="ri-logout-box-r-line"></i> <span data-key="t-users">Logout</span>
                        </a>
                    </li>
<!--                    <li class="nav-item">-->
<!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
<!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
<!--                        </a>-->
<!--                    </li>-->
                </ul>
            </div>
            <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
    </div>
    <!-- Left Sidebar End -->
    <!-- Vertical Overlay-->
    <div class="vertical-overlay"></div>

    <!-- ============================================================== -->
    <!-- Start right Content here -->
    <!-- ============================================================== -->
    <div class="main-content">

<div class="page-content">
    <div class="container-fluid">

        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">Scan Slip</h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item active">Scan Slip</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card"> 
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">

                                <div>
                                    <label for="inputFileSlip" class="form-label">Upload Slip</label>
                                    <div class="input-group">
                                        <input type="file" class="form-control" id="inputFileSlip" aria-describedby="inputFileSlipButton" accept="image/*" aria-label="Upload">
                                    </div>
                                </div>
                                <div id="imagePreview"></div>
                                
                            </div>

                            <div class="col-md-8" id="vueContainer" v-cloak>
                               <div class="row">
                                    <div class="col-md-12"  v-if="data_slip!=null">
                                        <div class="mb-4 table table-responsive">
                                            <label for="basiInput" class="form-label">รายการที่พบในระบบ</label>
                                            <table class="table table-nowrap mb-2" v-for="item in statement">
                                                <tbody >
                                                    <tr v-if="statement.length > 0">
                                                        <td class="text-left bg-soft-secondary" v-cloak>วันที่</td>
                                                        <td class="text-right" v-cloak>{{moment(item.txn_date).format('DD/MM/YYYY HH:mm:ss')}}</td>
                                                    </tr>
                                                    <tr v-if="statement.length > 0">
                                                        <td class="text-left bg-soft-secondary" v-cloak>บัญชีต้นทาง</td>
                                                        <td class="text-right" v-cloak>{{item.txn_bank_name}} {{item.txn_acc_4last}}</td>
                                                    </tr>
                                                    <tr v-if="statement.length > 0">
                                                        <td class="text-left bg-soft-secondary" v-cloak>ชื่อบัญชีปลายทาง (บริษัทฯ)</td>
                                                        <td class="text-right" v-cloak>{{item.account_name}} {{item.masked_account_no}}</td>
                                                    </tr>
                                                    <tr v-if="statement.length > 0">
                                                        <td class="text-left bg-soft-secondary" v-cloak>Promtpay (บริษัทฯ)</td>
                                                        <td class="text-right" v-cloak> {{item.masked_promtpay_no}}</td>
                                                    </tr>
                                                    <tr v-if="statement.length > 0">
                                                        <td class="text-left bg-soft-warning" v-cloak>ชื่อร้านค้า</td>
                                                        <td class="text-right" v-if="item.agent_id != null"   v-cloak>{{item.merchant_name}} ({{item.agent_id}})</td>
                                                        <td class="text-right text-danger" v-if="item.agent_id == null" v-cloak>*ไม่พบร้านค้า</td>
                                                        <!-- <td class="text-right text-danger" v-if="item.agent_id != null && item.agent_id != myagent_id" v-cloak>**อยู่ในร้านค้าอื่น ({{item.agent_id}})</td>  -->
                                                        
                                                    </tr>
                                                    <tr v-if="statement.length > 0">
                                                        <td class="text-left bg-soft-warning" v-cloak>สถานะ</td>
                                                        <td class="text-right"   v-cloak class="texn-center" v-html="handleShowStatus(item)"></td>
                                                    </tr>
                                                    <tr v-if="qrdata != '' && statement.length == 0" v-cloak>
                                                        <td colspan="5" class="text-center text-danger" style="font-size:20px">ไม่พบรายการจากธนาคาร กรุณารอไม่เกิน 10 นาที <br />หากยังไม่พบรายการให้ส่งสลิปมาให้เจ้าหน้าที่ตรวจสอบ</td> 
                                                    </tr>
                                                    <tr v-if="qrdata == ''" v-cloak>
                                                        <td colspan="5" class="text-center text-danger" style="font-size:20px">-</td> 
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-12 text-center" v-else>
                                        <span class="text-primary" style="font-size:16px;">** กรุณาอัพโหลดสลิปที่เมนู STEP 1 Import Slip ก่อนนำมาตรวจสอบ **</span>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="card" v-if="data_slip != null"> 
                                            <div class="card-header align-items-center d-flex">
                                                <h4 class="card-title mb-0 flex-grow-1">ข้อมูลจาก QRCode</h4>
                                            </div>
                                            <div class="card-body">
                                                <div class="table table-responsive"> 
                                                    <table class="table table-striped"> 
                                                        <tbody>
                                                            <tr>
                                                                <th>Date Time</th>
                                                                <td class="text-right" v-cloak>{{moment(data_slip.transactionDateTime).format('DD/MM/YYYY HH:mm:ss')}}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>Amount</th>
                                                                <td class="text-right" v-cloak>
                                                                    {{data_slip.amount.amount??'-'}} {{data_slip.amount.currency.displayName??''}}
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th>From Account</th>
                                                                <td class="text-right" v-cloak>
                                                                    <span v-if="data_slip.fromBankName!='' && data_slip.fromBankName!=null">{{data_slip.fromBankName??''}}<br /></span>
                                                                    <span v-if="data_slip.fromAccountNo!='' && data_slip.fromAccountNo!=null">{{data_slip.fromAccountNo??''}}<br /></span>
                                                                    <span v-if="data_slip.fromAccountName!='' && data_slip.fromAccountName!=null">{{data_slip.fromAccountName??''}}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th>To Account</th>
                                                                <td class="text-right" v-cloak>
                                                                    <span v-if="data_slip.toBankName!='' && data_slip.toBankName!=null">{{data_slip.toBankName??''}}<br /></span>
                                                                    <span v-if="data_slip.toProxyId!='' && data_slip.toProxyId!=null">{{data_slip.toProxyId??''}}<br /></span>
                                                                    <span v-if="data_slip.toAccountName!='' && data_slip.toAccountName!=null">{{data_slip.toAccountName??''}}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th>Transaction Ref</th>
                                                                <td class="text-right" v-cloak>
                                                                    {{data_slip.transactionRef??'-'}}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                               </div>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

         

    </div>
</div>


<div class="modal fade" id="modalMoveToAgent" tabindex="-1" aria-labelledby="modalMoveToAgent" aria-modal="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="">โยกเข้าร้านค้า</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="javascript:void(0);" id="formMoveToAgent" enctype="multipart/form-data">
                    <input type="hidden" name="moveToAgentStatementID" id="moveToAgentStatementID" class="form-control" value="">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row g-3">
                                <strong>ข้อมูลรายการฝาก</strong>
                                
                                <input type="hidden" class="form-control" name="moveToAgentAgentID" id="moveToAgentAgentID" value="701" />
                                <div class="col-md-6">
                                    <div>
                                        <label for="moveToAgentTxnDate" class="form-label">Statement Date</label>
                                        <input type="text" class="form-control" name="moveToAgentTxnDate" id="moveToAgentTxnDate" disabled placeholder="">
                                    </div>
                                </div><!--end col-->
                                <div class="col-md-6">
                                    <div>
                                        <label for="moveToAgentTxn4Last" class="form-label">From Account</label>
                                        <input type="text" class="form-control" name="moveToAgentTxn4Last" id="moveToAgentTxn4Last" disabled placeholder="">
                                    </div>
                                </div><!--end col-->
                                <div class="col-md-6">
                                    <div>
                                        <label for="moveToAgentTxnAmount" class="form-label">Amount</label>
                                        <input type="text" class="form-control" name="moveToAgentTxnAmount" id="moveToAgentTxnAmount" disabled placeholder="">
                                    </div>
                                </div><!--end col-->
                                <div class="col-md-6">
                                    <label for="moveToAgentUsername" class="form-label">Username/User ID </label>
                                    <input type="text" class="form-control" name="moveToAgentUsername" id="moveToAgentUsername" maxlength="75" >
                                </div>
                            </div><!--end col-->
                                <div class="col-md-6">
                                    <div>
                                        <label for="moveToAgentSlipImage" class="form-label">สลิป</label>
                                        <input type="file" class="form-control" name="moveToAgentSlipImage" id="moveToAgentSlipImage" accept="image/jpeg,image/png" onchange="preview_image(event, this);">
                                        <img id="show_moveToAgentSlipImage" class="previewImage" style="max-height: 100px;max-width: 100%;">
                                    </div>
                                </div><!--end col-->
                            </div>
                        </div>
                         
                    </div>
                    <div class="modal-footer">
                        <div class="row g-3">

                            <div class="col-md-12">
                                <div class="hstack gap-2 justify-content-end">
                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">ปิด</button>
                                    <button type="submit" class="btn btn-primary" onclick="return confirmMoveToAgent();">ยืนยันโยกเข้าร้านค้า</button>
                                </div>
                            </div><!--end col-->

                        </div><!--end row-->
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

 
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                Copyright <script>document.write(new Date().getFullYear())</script> © member.sugarpay66.io            </div>
            <div class="col-sm-6">
                
            </div>
        </div>
    </div>
</footer>
</div>
<!-- end main content-->

</div>
<!-- END layout-wrapper -->

<!--start back-to-top-->
<button onclick="topFunction()" class="btn btn-primary btn-icon" id="back-to-top">
    <i class="ri-arrow-up-line"></i>
</button>
<!--end back-to-top-->

<!--preloader-->
<div id="preloader">
    <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

<div class="modal fade" id="modelPreviewImage" tabindex="-1" aria-labelledby="modelPreviewImage" aria-modal="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">Preview Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="" id="imgPreviewImage" style="width: 100%;" alt="">
            </div>
        </div>
    </div>
</div>



<!-- JAVASCRIPT -->
<script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="public/assets/libs/simplebar/simplebar.min.js"></script>
<script src="public/assets/libs/node-waves/waves.min.js"></script>
<script src="public/assets/libs/feather-icons/feather.min.js"></script>
<script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
<script src="public/assets/js/plugins.js?date=202403042044"></script>

<script src="public/assets/js/pages/notifications.init.js"></script>

<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

<!--datatable js-->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

<!-- Sweet Alerts js -->
<script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

<script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>


<!-- Sweet alert init js-->
<script src="public/assets/js/pages/sweetalerts.init.js"></script>

<!-- Moment js -->
<script src="public/assets/libs/moment/moment.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js" integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

<!-- dropzone min -->
<script src="public/assets/libs/dropzone/dropzone-min.js"></script>
<!-- filepond js -->
<script src="public/assets/libs/filepond/filepond.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

<!-- App js -->
<script src="public/assets/js/app.js?t=1746205200"></script>
<script>

 
    var tableDeposit =  { table : '',tableDrawStatus :true, tooltipTriggerList : '',tooltipList : ''};
    document.addEventListener("DOMContentLoaded",function(){
        new DataTable(".alternative-pagination",{
        })
        
    })
    $(document).ready(function () {
                        
                
    });

    function unshowAnnouncement(id){
        setCookie("cookie_announcement", id, 7); 
        $("#modalAnnouncement").modal('hide')
    }

    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    $(document).on('click','img.previewImage',function(){
        let src = $(this).attr('src');
        $("#imgPreviewImage").attr('src', src);

        $("#modelPreviewImage").modal("show");
    });

    function formatNumber(value,digit = 2){
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
            maximumFractionDigits: digit
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    function preview_image(event, obj) {
        var output = document.getElementById('show_' + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
    }

   
    function copyButton(elm, copyText, afterTextButton){
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
    }
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(
                function () { 
                    console.log('Text copied to clipboard'); 
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: 'Text copied to clipboard',
                        showConfirmButton: false,
                        timer: 1500,
                        
                    })
                },
                function (err) { console.error('Could not copy text: ', err); }
            );
        } else {
            let input = document.createElement('textarea');
            input.style.position = 'fixed';
            input.style.zIndex = 9999;
            input.value = text;
            document.body.appendChild(input);
            input.select();
            input.focus();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

    }


</script>

<script src="public/assets/libs/jsQr/jimp.min.js"></script>
<script src="public/assets/libs/jsQr/jsQR.min.js"></script>


<!-- Vue -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.3.4/vue.min.js"></script>


<script>
    var url_ajax = '/scanslip/';


    var app = new Vue({
		el: "#vueContainer", 
		created(){ 
		},
		data : function(){
            return {
                myagent_id : parseInt('701'),
                qrdata : '',
                statement : [],
                data_slip : null
            }
        },
        methods : {
            handleShowStatus : function(data){
                if(data.match_result == "MATCH"){
                    if(data.status == 'SUCCESS' && (data.match_by == null || data.match_by == '')){
                        return `<span class="badge text-dark bg-soft-success">Match</span><br>${data.order_id??''}<br>${data.customer_username??''}`;
                    }else if(data.status == 'SUCCESS' && data.match_by != null && data.match_by != ''){
                        return `<span class="badge text-dark bg-soft-secondary">Move</span><br>${data.order_id??''}<br>${data.customer_username??''}`;
                    }else  if(data.status == "REFUSE"){
                        return `<span class="badge text-bg-danger">REFUSE</span>`;
                    }else if(data.status == "WAIT_CONFIRM"){
                        return `<span class="badge text-dark bg-soft-primary">Wait confirm</span>`;
                    }else{
                        return `<span class="badge text-dark bg-soft-success">Match</span><br>${data.order_id??''}<br>${data.customer_username??''}`;
                    }
                }
                else if(data.match_result == "REFUND" ){
                    if(data.transfer_status == "SUCCESS"){
                        return `<span class="badge text-light bg-success">โอนคืนลูกค้า</span>`;
                    }else if(data.transfer_status == "PENDING"){
                        return `<span class="badge text-light bg-warning">โอนคืน รอดำเนินการ</span>`;
                    }else if(data.transfer_status == "FAIL"){
                        return `<span class="badge text-light bg-danger">โอนคืน ไม่สำเร็จ</span>`;
                    }else if(data.transfer_status == "PROCESS"){
                        return `<span class="badge text-light bg-primary">โอนคืน กำลังดำเนินการ</span>`;
                    }else{
                        return `<span class="badge bg-info">โอนคืนลูกค้า </span>`;
                    }
                }
                else if(data.match_result == "SKIP" ){
                    var btn = '';
                    if(data.agent_id == null){
                        btn = ` <b>|</b> <button type="button" onclick="moveToAgent(${data.statement_id});" class="btn btn-sm btn-success" style="padding: 0 4px;">กดโยกเข้าร้านค้า</button> `;
                    }
                    return `<span class="badge text-dark bg-soft-danger">Skip</span>${btn}`;
                }
                else if(data.match_result == "PENDING" ){
                    var btn = '';
                    if(data.agent_id == null){
                        btn = ` <b>|</b> <button type="button" onclick="moveToAgent(${data.statement_id});" class="btn btn-sm btn-success" style="padding: 0 4px;">กดโยกเข้าร้านค้า</button> `;
                    }
                    return `<span class="badge text-dark bg-soft-warning">Unknown</span>${btn}`;
                }else{
                    return '<span>valid 102</span>'
                }
            }
        },
        watch: {  }, 
	});  

    const fileInput = document.getElementById('inputFileSlip');
    const fileInputModal = document.getElementById('moveToAgentSlipImage');
    const imagePreview = document.getElementById('imagePreview');

    

    fileInput.addEventListener('change', async function (event) {
        const file = event.target.files[0];
        app.statement = [];
        app.data_slip = null;
        if (file && file.type.startsWith('image/')) {
            
            try {
                const buffer = await readFileAsBuffer(file);
                let qrCodeValue = await readQRCodeFromFile(buffer,800);
                if(!qrCodeValue){
                    qrCodeValue = await readQRCodeFromFile(buffer,1200);
                    if(!qrCodeValue){
                        qrCodeValue = await readQRCodeFromFile(buffer,1500);
                        if(!qrCodeValue){
                            qrCodeValue = await readQRCodeFromFile(buffer,1980);
                            if(!qrCodeValue){
                                qrCodeValue = await readQRCodeFromFile(buffer,2380);
                                if(!qrCodeValue){
                                    qrCodeValue = await readQRCodeFromFile(buffer,3090);
                                }
                            }
                        }
                    }
                }
                if (qrCodeValue) {
                    app.qrdata = '';
                    app.statement = [];
                    app.data_slip = null;

                    app.qrdata = qrCodeValue;
                    $.blockUI({ css: { backgroundColor: '#fff', color: '#000' , borderColor : '#fff'  } , message : 'กำลังโหลดข้อมูล' });
                    await getDataWithTextQrcode(qrCodeValue)
                    setTimeout(() => {
                        $.unblockUI();
                    }, 300);
                } else {
                    console.log('qrCodeValue',qrCodeValue)
                    console.log('No QR code detected.');
                    // alert('ไม่พบ Qrcode หรือภาพไม่ชัดเจน กรุณาอัพโหลดสลิปใหม่');
                }

                const reader = new FileReader();
                reader.onload = function (e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.width = '100%';
                    imagePreview.innerHTML = '';
                    imagePreview.appendChild(img);
                };
                reader.readAsDataURL(file);
                
            } catch (error) {
            console.error('Error reading or decoding QR code:', error);
            }
        } else {
            imagePreview.innerHTML = 'Please select an image file.';
        }
    });

    async function getDataWithTextQrcode(qrCodeValue){
        var formdata = new FormData();
        formdata.append('qrdata',qrCodeValue)
        var requestOptions = {
            method: 'POST',
            body: formdata,
            redirect: 'follow'
        };
        var res = await fetch( url_ajax + 'api/read_qrdata', requestOptions)
        .then(response => response.json()) 
        if(res.error == 0){
            app.statement = res.statementData;
            app.data_slip = res.result;
        }else{
            app.statement = [];
            app.data_slip = null;
            // alert('ไม่พบข้อมูลกรุณาอัพโหลดสลิปใหม่');
        }
    }

    function readFileAsBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = function (e) {
            const buffer = e.target.result;
            resolve(buffer);
            };

            reader.onerror = function (e) {
            reject(e.target.error);
            };

            reader.readAsArrayBuffer(file);
        });
    }

    async function readQRCodeFromFile(buffer,setwidth = 800) {
        try {
            const image = await Jimp.read(buffer);
            const isHorizontal = image.getWidth() > image.getHeight();
            const ratio = isHorizontal ? image.getWidth() / image.getHeight() : image.getHeight() / image.getWidth();
            const width = setwidth; // set the width you want
            const height = isHorizontal ? width / ratio : width * ratio;

            image.resize(width, height);

            const { data, width: qrWidth, height: qrHeight } = image.bitmap;
            const qrCode = jsQR(data, qrWidth, qrHeight);

            return qrCode ? qrCode.data : null;
        } catch (error) {
            throw new Error('Error reading or decoding QR code');
        }
    }

    // Clone Bankstatement Pending
    var urlBankStmPending = '/bank_statement_pendingv2/v2/';
    function moveToAgent(statement_id){
        if(statement_id != ''){
            $.ajax({
                url: urlBankStmPending + 'statement_detail',
                type: "post",
                async: false,
                data: {
                    statement_id: statement_id,
                },
                dataType:"json",
                success: function (res) {
                    $("#formMoveToAgent")[0].reset();
                    if(res.error == 0){
                        $("#moveToAgentStatementID").val(res.result.statement_id);
                        $("#moveToAgentTxnDate").val(moment(res.result.txn_date).format("YYYY-MM-DD HH:mm:ss") );
                        if(res.result.txn_remark.indexOf('NATID') >= 0){
                            $("#moveToAgentTxn4Last").val(``);
                        }else{
                            $("#moveToAgentTxn4Last").val(`${res.result.txn_bank_name} ${res.result.txn_acc_4last}`);
                        }


                        $("#moveToAgentTxnAmount").val(formatNumber(res.result.txn_amount,2));

                        fileInputModal.files = fileInput.files;

                        $("#modalMoveToAgent").modal("show");
                    }else{
                        Swal.fire({
                            icon: 'warning',
                            title: `เกิดข้อผิดพลาด ไม่พบข้อมูล`,
                            confirmButtonText: "ปิด"
                        })
                    }
                }
            });
        }

        return false;
    }


    function confirmMoveToAgent(){
        let moveToAgentAgentID = $("#moveToAgentAgentID").val().trim();
        if(moveToAgentAgentID == ''){
            //  || $("#moveToAgentSlipImage").get(0).files.length === 0
            Swal.fire({
                icon: 'warning',
                title: `กรุณากรอกข้อมูลให้ครบถ้วน`,
                confirmButtonText: "ปิด"
            })

            return false;
        }
        Swal.fire({
            title: 'ยืนยันโยกรายการฝาก',
            text: "คุณต้องการโยกกรายการฝากเข้าร้านค้านี้ใช่หรือไม่",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                saveMoveToAgent();
            }
        })
    }


    function saveMoveToAgent(){

        const formData = new FormData($('#formMoveToAgent')[0]);

        $.ajax({
            url: urlBankStmPending + 'move_to_agent',
            type: "post",
            async: false,
            data: formData,
            contentType: false,
            processData: false,
            success: async function (res) {
                if(res.error == 0){

                    Swal.fire({
                        icon: 'success',
                        title: `โยกรายการฝากเข้าร้านค้าเรียบร้อย`,
                        confirmButtonText: "ปิด"
                    }).then((result) => {
                        // editMember(editMemberMemberID)
                        $("#modalMoveToAgent").modal("hide");
                    })
                }else{
                    Swal.fire({
                        icon: 'warning',
                        title: 'Warning',
                        text: 'เกิดข้อผิดพลาด ไม่พบข้อมูลรายการฝาก',
                        showConfirmButton: true,
                        confirmButtonText: 'รับทราบ'
                    })
                }

                $.blockUI({ css: { backgroundColor: '#fff', color: '#000' , borderColor : '#fff'  } , message : 'กำลังโหลดข้อมูล' });
                    await getDataWithTextQrcode(app.qrdata)
                setTimeout(() => {
                    $.unblockUI();
                }, 300);
            }
        });

        return false;
    }



</script>
</body>

</html>

