<!DOCTYPE html>
<html
  lang="en"
  data-layout="vertical"
  data-topbar="light"
  data-sidebar="dark"
  data-sidebar-size="lg"
  data-sidebar-image="none"
  data-preloader="disable"
>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="sugarpay66 payment" />
    <meta name="keywords" content="sugarpay66 payment" />
    <meta name="author" content="member.sugarpay66.io" />
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta
      property="og:image"
      content="/public/assets//images/logo/android-chrome-512x512.png"
    />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>

    <style>
      :root {
        --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
        --theme-color_primary: #6e5c60;
      }
    </style>

    <!-- For IE6+ -->
    <link
      rel="shortcut icon"
      href="public/assets/images/logo/favicon.ico"
      type="image/x-icon"
    />

    <!-- For all other browsers -->
    <link
      rel="icon"
      href="public/assets/images/logo/favicon.ico"
    />

    <!-- Different sizes -->
    <link
      rel="icon"
      href="public/assets/images/logo/favicon-16x16.png"
      sizes="16x16"
    />
    <link
      rel="icon"
      href="public/assets/images/logo/favicon-32x32.png"
      sizes="32x32"
    />

    <!-- For Modern Browsers with PNG Support -->
    <link
      rel="icon"
      type="image/png"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link
      rel="icon"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link
      rel="apple-touch-icon"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- Favicon without reflective shine -->
    <link
      rel="apple-touch-icon-precomposed"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- jsvectormap css -->
    <link
      href="public/assets/libs/jsvectormap/css/jsvectormap.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!--Swiper slider css-->
    <link
      href="public/assets/libs/swiper/swiper-bundle.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- Sweet Alert css-->
    <link
      href="public/assets/libs/sweetalert2/sweetalert2.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <link
      href="public/assets/libs/flatpickr/flatpickr.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link
      href="public/assets/css/bootstrap.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- Icons Css -->
    <link
      href="public/assets/css/icons.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- App Css-->
    <link
      href="public/assets/css/app.css?t=1746271695"
      rel="stylesheet"
      type="text/css"
    />

    <!-- custom Css-->
    <link
      href="public/assets/css/custom.css?t=1746271695"
      rel="stylesheet"
      type="text/css"
    />

    <!--datatable css-->
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css"
    />
    <!--datatable responsive css-->
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css"
    />
    <style type="text/css">
      .text-bold {
        font-weight: bold;
      }
      .text-right {
        text-align: right;
      }
      .modal-xl {
        max-width: 1300px !important;
      }
      #modalAnnouncement img {
        width: 100%;
      }
      #modalAnnouncement p {
        margin-top: 0;
        margin-bottom: 0.2rem;
      }
    </style>
    <style>
      .mouseOverlay {
        cursor: pointer;
      }
    </style>
  </head>

  <body>
    <!-- Begin page -->
    <div id="layout-wrapper">
      <header id="page-topbar">
        <div class="layout-width">
          <div class="navbar-header">
            <div class="d-flex">
              <!-- LOGO -->
              <div class="navbar-brand-box horizontal-logo">
                <a href="" class="logo logo-dark">
                  <span class="logo-sm">
                    <img
                      src="public/assets/images/logo/logo-sm.png"
                      alt=""
                      height="22"
                    />
                  </span>
                  <span class="logo-lg">
                    <img
                      src="public/assets/images/logo/logo.png"
                      alt=""
                      height="48"
                    />
                  </span>
                </a>

                <a href="index.html" class="logo logo-light">
                  <span class="logo-sm">
                    <img
                      src="public/assets/images/logo/logo-sm.png"
                      alt=""
                      height="22"
                    />
                  </span>
                  <span class="logo-lg">
                    <img
                      src="public/assets/images/logo/logo.png"
                      alt=""
                      height="48"
                    />
                  </span>
                </a>
              </div>

              <button
                type="button"
                class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                id="topnav-hamburger-icon"
              >
                <span class="hamburger-icon">
                  <span></span>
                  <span></span>
                  <span></span>
                </span>
              </button>
            </div>

            <div class="d-flex align-items-center">
              <div class="ms-1 header-item d-none d-sm-flex">
                <button
                  type="button"
                  class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                  data-toggle="fullscreen"
                >
                  <i class="bx bx-fullscreen fs-22"></i>
                </button>
              </div>
              <div class="dropdown ms-sm-3 header-item topbar-user">
                <button
                  type="button"
                  class="btn"
                  id="page-header-user-dropdown"
                  data-bs-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <span class="d-flex align-items-center">
                    <img
                      class="rounded-circle header-profile-user"
                      src="public/assets/images/logo/logo-sm.png"
                      alt="Header Avatar"
                    />
                    <span class="text-start ms-xl-2">
                      <span
                        class="d-none d-xl-inline-block ms-1 fw-medium user-name-text"
                        >tiger-001</span
                      >
                      <span
                        class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"
                      ></span>
                    </span>
                  </span>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                  <!-- item-->
                  <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                  <a
                    class="dropdown-item"
                    href="login/logout"
                    ><i
                      class="mdi mdi-logout text-muted fs-16 align-middle me-1"
                    ></i>
                    <span class="align-middle" data-key="t-logout"
                      >ออกจากระบบ</span
                    ></a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- ========== App Menu ========== -->
      <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
          <!-- Dark Logo-->
          <a href="" class="logo logo-dark">
            <span class="logo-sm">
              <img
                src="public/assets/images/logo/logo-sm.png"
                alt=""
                height="22"
              />
            </span>
            <span class="logo-lg">
              <img
                src="public/assets/images/logo/logo.png"
                alt=""
                height="48"
              />
            </span>
          </a>
          <!-- Light Logo-->
          <a href="" class="logo logo-light">
            <span class="logo-sm">
              <img
                src="public/assets/images/logo/logo-sm.png"
                alt=""
                height="22"
              />
            </span>
            <span class="logo-lg">
              <img
                src="public/assets/images/logo/logo.png"
                alt=""
                height="48"
              />
            </span>
          </a>
          <button
            type="button"
            class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
            id="vertical-hover"
          >
            <i class="ri-record-circle-line"></i>
          </button>
        </div>

        <div id="scrollbar">
          <div class="container-fluid">
            <div id="two-column-menu"></div>
            <ul class="navbar-nav mt-3" id="navbar-nav">
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="dashboard.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Dashboard</span>
                </a>
              </li>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="transaction.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Transaction</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="withdraw_approve.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Withdraw Approve</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="bank_statement.html"
                >
                  <i class="ri-bank-line"></i>
                  <span data-key="t-users">Statement</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="summary_transfer.html"
                >
                  <i class="ri-file-transfer-line"></i>
                  <span data-key="t-users">Settlement</span>
                </a>
              </li>

              <a
                class="nav-link menu-link"
                href="#sidebarReport"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarReport"
              >
                <i class="ri-article-line"></i>
                <span data-key="t-report">Report</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarReport">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="report_agent_daily_realtime.html"
                      class="nav-link"
                      data-key="t-report"
                      >Daily Summary Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_withdraw_channel.html"
                      class="nav-link"
                      data-key="t-users"
                      >Withdraw Channel Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_withdraw.html"
                      class="nav-link"
                      data-key="t-report"
                      >Withdraw Activity Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_deposit.html"
                      class="nav-link"
                      data-key="t-report"
                      >Deposit Activity Report</a
                    >
                  </li>

                  <!--                            <li class="nav-item">-->
                  <!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
                  <!--                            </li>-->
                </ul>
              </div>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="withdraw_fund.html"
                >
                  <i class="ri-file-transfer-line"></i>
                  <span data-key="t-users">Withdraw Fund</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="scanslip-step-import.html"
                >
                  <i class="ri-qr-code-fill"></i>
                  <span data-key="t-users">STEP 1 Import Slip</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="scanslip-step-verify.html"
                >
                  <i class="ri-qr-code-fill"></i>
                  <span data-key="t-users">STEP 2 Verify Slip</span>
                </a>
              </li>

              <a
                class="nav-link menu-link"
                href="#sidebarTools"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarTools"
              >
                <i class="ri-article-line"></i>
                <span data-key="t-report">Tools</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarTools">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="black_list.html"
                      class="nav-link"
                      data-key="t-report"
                      >Blacklist Manage</a
                    >
                  </li>
                </ul>
              </div>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="profile.html"
                >
                  <i class="ri-folder-user-line"></i>
                  <span data-key="t-users">Profile</span>
                </a>
              </li>
              <a
                class="nav-link menu-link"
                href="#sidebarSetupUser"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarSetupUser"
              >
                <i class="ri-folder-user-line"></i>
                <span data-key="t-setup-user">Settings</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarSetupUser">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="user_group.html"
                      class="nav-link"
                      data-key="t-user-group"
                    >
                      User Groups
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      href="user_permission.html"
                      class="nav-link"
                      data-key="t-permission"
                    >
                      Group Permission
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      href="user"
                      class="nav-link"
                      data-key="t-users"
                    >
                      Sub Users
                    </a>
                  </li>
                </ul>
              </div>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="login/logout"
                >
                  <i class="ri-logout-box-r-line"></i>
                  <span data-key="t-users">Logout</span>
                </a>
              </li>
              <!--                    <li class="nav-item">-->
              <!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
              <!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
              <!--                        </a>-->
              <!--                    </li>-->
            </ul>
          </div>
          <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
      </div>
      <!-- Left Sidebar End -->
      <!-- Vertical Overlay-->
      <div class="vertical-overlay"></div>

      <!-- ============================================================== -->
      <!-- Start right Content here -->
      <!-- ============================================================== -->
      <div class="main-content">
        <div class="page-content">
          <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
              <div class="col-12">
                <div
                  class="page-title-box d-sm-flex align-items-center justify-content-between"
                >
                  <h4 class="mb-sm-0">Transaction</h4>

                  <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                      <li class="breadcrumb-item active">Transaction</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
            <!-- end page title -->

            <div class="row h-100">
              <div class="col-lg-3 col-md-6">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex align-items-center">
                      <div class="avatar-sm flex-shrink-0">
                        <span
                          class="avatar-title bg-light text-primary rounded-circle fs-3"
                        >
                          <i
                            class="ri-money-dollar-circle-fill align-middle"
                          ></i>
                        </span>
                      </div>
                      <div class="flex-grow-1 ms-3">
                        <p
                          class="text-uppercase fw-semibold fs-12 text-muted mb-1"
                        >
                          Deposit Balance
                        </p>
                        <h4 class="mb-0">
                          <span
                            class="counter-value text-primary"
                            id="total_balance_amount"
                            data-target=""
                          ></span>
                        </h4>
                      </div>
                      <div class="flex-shrink-0 align-self-end">
                        <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                      </div>
                    </div>
                  </div>
                  <!-- end card body -->
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-3 col-md-6">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex align-items-center">
                      <div class="avatar-sm flex-shrink-0">
                        <span
                          class="avatar-title bg-light text-primary rounded-circle fs-3"
                        >
                          <i
                            class="ri-money-dollar-circle-fill align-middle"
                          ></i>
                        </span>
                      </div>
                      <div class="flex-grow-1 ms-3">
                        <p
                          class="text-uppercase fw-semibold fs-12 text-muted mb-1"
                        >
                          Withdraw Balance
                        </p>
                        <h4 class="mb-0">
                          <span
                            class="counter-value text-danger"
                            id="total_withdraw_balance"
                            data-target=""
                          ></span>
                        </h4>
                      </div>
                      <div class="flex-shrink-0 align-self-end">
                        <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                      </div>
                    </div>
                  </div>
                  <!-- end card body -->
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
            </div>

            <div class="row">
              <div class="col-lg-12">
                <div class="card">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Filter</h4>
                    <div class="flex-shrink-0">
                      <a
                        href="javascript:void(0)"
                        onclick="makePayment();"
                        class="btn btn-primary"
                        style="display: initial"
                        ><i class="mdi mdi-plus"></i> Make Payment</a
                      >
                      <a
                        href="javascript:void(0)"
                        onclick="makeWithdraw();"
                        class="btn btn-warning"
                        style="display: initial"
                        ><i class="mdi mdi-transfer"></i> Make Withdraw</a
                      >
                    </div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview mb-2">
                      <form action="#" id="form_search" method="post">
                        <div class="row g-3">
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Start Date</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="start_date"
                              value="2025-05-03 00:00"
                            />
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >End Date</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="end_date"
                              value="2025-05-03 23:59"
                            />
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label">Type</label>
                            <select
                              id="transaction_type_search"
                              class="form-select"
                            >
                              <option value="ALL" selected>All</option>
                              <option value="DEPOSIT">DEPOSIT</option>
                              <option value="WITHDRAW">WITHDRAW</option>
                            </select>
                          </div>
                          <div class="col-lg-3 d-none">
                            <label for="field1" class="form-label"
                              >Deposit Type</label
                            >
                            <select
                              id="deposit_type_search"
                              class="form-select"
                            >
                              <option value="ALL" selected>All</option>
                              <option value="QRCODE">QRCODE</option>
                            </select>
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Status</label
                            >
                            <select id="status_search" class="form-select">
                              <option value="ALL" selected>All</option>
                              <option value="CREATE">CREATE</option>
                              <option value="ON_PROCESS">ON_PROCESS</option>
                              <option value="WAIT_CONFIRM">WAIT_CONFIRM</option>
                              <option value="SUCCESS">SUCCESS</option>
                              <option value="SUCCESS&CONFIRM">
                                SUCCESS & WAIT_CONFIRM
                              </option>
                              <option value="REFUSE">REFUSE</option>
                              <option value="BLACKLIST">BLACKLIST</option>
                              <option value="FAILED">FAILED</option>
                              <option value="MOVE">โยกเข้าร้านค้า</option>
                              <option value="AUTOMATCH">
                                จับคู่อัติโนมัติ
                              </option>
                            </select>
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Channel</label
                            >
                            <select id="channel_search" class="form-select">
                              <option value="ALL" selected>All</option>
                              <option value="WEB">WEB</option>
                              <option value="API">API</option>
                            </select>
                          </div>
                          <div class="col-lg-3">
                            <label for="txn_amount_search" class="form-label"
                              >จำนวนเงิน</label
                            >
                            <div class="input-group">
                              <input
                                type="text"
                                class="form-control number-only"
                                id="from_txn_amount_search"
                              />
                              <span class="input-group-text"> - </span>
                              <input
                                type="text"
                                class="form-control number-only"
                                id="to_txn_amount_search"
                              />
                            </div>
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Bank Account</label
                            >
                            <select
                              id="transaction_bank_acc_no"
                              class="form-select"
                            >
                              <option value="ALL" selected>All</option>
                              <option value="**********">
                                บริษัท วิวัฒน์ อินดีไซน์ จำกัด
                              </option>
                              <option value="**********">
                                บริษัท ซูเปอร์ทรีบิ้วตี้สโตร์ จำกัด
                              </option>
                              <option value="**********">
                                บจก.วุฒิกรช็อป 1998 (Pool)
                              </option>
                            </select>
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Text in table</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="text_search"
                              value=""
                            />
                          </div>
                        </div>
                        <div>
                          <div class="row g-3 mt-3">
                            <div class="col-12 text-center">
                              <button
                                type="button"
                                class="btn btn-success waves-effect waves-light"
                                onclick="get_datatable_list();"
                              >
                                <i class="mdi mdi-filter-plus-outline"></i>
                                Search
                              </button>
                              <button
                                type="reset"
                                class="btn btn-soft-dark waves-effect waves-light"
                                onclick="setTimeout(get_datatable_list,300)"
                              >
                                <i class="mdi mdi-filter-remove-outline"></i>
                                Clear
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-12">
                <div class="card">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      Result - Total Amount:
                      <span
                        class="badge text-bg-primary"
                        id="total_deposit_amount"
                      ></span>
                      | Total MDR Fee:
                      <span
                        class="badge text-bg-warning"
                        id="total_mdr_amount"
                      ></span>
                      | Total NET Amount:
                      <span
                        class="badge text-bg-success"
                        id="total_net_amount"
                      ></span>
                    </h4>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <!-- Datatable -->
                    <div class="table-responsive">
                      <table
                        id="datatable-list"
                        class="table nowrap dt-responsive align-middle table-hover table-bordered"
                        style="width: 100%"
                      >
                        <thead>
                          <!-- <tr>
                                    <th scope="col">Create Date</th>
                                    <th scope="col">Order Id</th>
                                    <th scope="col">Type</th>
                                    <th scope="col">Amount</th>
                                    <th scope="col">MDR</th>
                                    <th scope="col">Net</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Transaction Date</th>
                                    <th scope="col">Bank</th>
                                    <th scope="col">Account No</th>
                                    <th scope="col">Cust Name</th>
                                    <th scope="col">UserName</th>
                                </tr> -->
                          <tr>
                            <th scope="col" class="bg-soft-dark">
                              Create Date <br />Transaction Date
                            </th>
                            <th scope="col" class="bg-soft-dark">Merchant</th>
                            <th scope="col" class="bg-soft-dark">
                              Order Id<br />Username
                            </th>
                            <th scope="col" class="bg-soft-dark">Type</th>
                            <th scope="col" class="bg-soft-dark">Amount</th>
                            <th scope="col" class="bg-soft-dark">MDR</th>
                            <th scope="col" class="bg-soft-dark">Net</th>
                            <th scope="col" class="bg-soft-dark">
                              Status<br />Slip
                            </th>
                            <th scope="col" class="bg-soft-dark">
                              API Parameter
                            </th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!--end col-->
            </div>
            <!--end row-->
          </div>
          <!-- container-fluid -->
        </div>
        <!-- End Page-content -->

        <div
          class="modal fade"
          id="modalMakePayment"
          data-bs-backdrop="static"
          data-bs-keyboard="false"
          tabindex="-1"
          aria-labelledby="modalMakePayment"
          aria-modal="true"
        >
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">
                  Make Payment
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <form
                  action="javascript:void(0);"
                  id="formMakePayment"
                  enctype="multipart/form-data"
                >
                  <div class="row g-3">
                    <div class="col-md-6">
                      <div>
                        <label for="makePaymentType" class="form-label"
                          >Type <span class="text-danger">*</span></label
                        >
                        <select
                          name="makePaymentType"
                          id="makePaymentType"
                          class="form-select"
                          required
                        >
                          <option value="QRCODE">QRCODE</option>
                        </select>
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="makePaymentOrderId" class="form-label"
                          >Order ID <span class="text-danger">*</span></label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="makePaymentOrderId"
                          id="makePaymentOrderId"
                          required
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="makePaymentAmount" class="form-label"
                          >Amount <span class="text-danger">*</span></label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="makePaymentAmount"
                          id="makePaymentAmount"
                          required
                          placeholder="100.00"
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="makePaymentBankAccountName"
                          class="form-label"
                          >Bank Account Name
                          <span class="text-danger requireTransfer">*</span>
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          name="makePaymentBankAccountName"
                          id="makePaymentBankAccountName"
                          placeholder="สมชาย ใจดี"
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="makePaymentBankAccountNo" class="form-label"
                          >Bank Account No
                          <span class="text-danger requireTransfer">*</span>
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          name="makePaymentBankAccountNo"
                          id="makePaymentBankAccountNo"
                          maxlength="13"
                          placeholder="xxxxxxxxx"
                          required
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="makePaymentBankName" class="form-label"
                          >Bank Name
                          <span class="text-danger requireTransfer">*</span>
                        </label>
                        <select
                          name="makePaymentBankName"
                          id="makePaymentBankName"
                          class="form-select"
                          required
                        >
                          <option value="">Please select bank</option>
                          <option value="034">
                            BAAC-ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร
                          </option>
                          <option value="025">BAY-ธนาคารกรุงศรีอยุธยา</option>
                          <option value="002">BBL-ธนาคารกรุงเทพ</option>
                          <option value="901">CDM-โอนเงินจากตู้ ATM</option>
                          <option value="022">CIMB-ธนาคารซีไอเอ็มบีไทย</option>
                          <option value="017">CITI-ซิตี้แบงก์</option>
                          <option value="033">
                            GHB-ธนาคารอาคารสงเคราะห์ (ธอส.)
                          </option>
                          <option value="030">GSB-ธนาคารออมสิน</option>
                          <option value="066">ISBT-ธนาคารอิสลาม</option>
                          <option value="004">KBANK-ธนาคารกสิกรไทย</option>
                          <option value="069">KKP- ธนาคาร เกียรตินาคิน</option>
                          <option value="006">KTB-ธนาคารกรุงไทย</option>
                          <option value="073">
                            LHBANK-ธนาคารแลนด์ แอนด์ เฮาส์
                          </option>
                          <option value="014">SCB-ไทยพาณิชย์</option>
                          <option value="018">
                            SMBC-ธนาคาร ซูมิโตโม มิตซุย แบงกิ้ง
                          </option>
                          <option value="071">
                            TCRB-ธนาคารไทยเครดิต เพื่อรายย่อย
                          </option>
                          <option value="067">TISCO-ธนาคารทิสโก้</option>
                          <option value="801">TRUE-True Wallet</option>
                          <option value="011">TTB-ธนาคารทหารไทยธนชาติ</option>
                          <option value="024">UOB-ธนาคารยูโอบี</option>
                        </select>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <div class="row g-3 mt-2" id="makePaymentSubmit">
                    <div class="col-md-12">
                      <div class="hstack gap-2 justify-content-center">
                        <button
                          type="submit"
                          class="btn btn-success"
                          onclick="return confirmMakePayment();"
                        >
                          <i class="mdi mdi-plus-thick"></i> Submit
                        </button>
                        <button
                          type="button"
                          class="btn btn-light"
                          data-bs-dismiss="modal"
                        >
                          <i class="mdi mdi-close-thick"></i> Cancel
                        </button>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <!--end row-->
                  <div
                    class="row g-3 mt-2"
                    id="makePaymentResult"
                    style="display: none"
                  >
                    <table class="table table-bordered table-striped">
                      <tbody>
                        <tr>
                          <td>Ref ID</td>
                          <td
                            class="text-right"
                            id="makePaymentResultRefId"
                          ></td>
                        </tr>
                        <tr>
                          <td>Order ID</td>
                          <td
                            class="text-right"
                            id="makePaymentResultOrderId"
                          ></td>
                        </tr>
                        <tr>
                          <td>Price</td>
                          <td
                            class="text-right text-danger text-bold"
                            style="font-size: 1rem"
                            id="makePaymentResultPrice"
                          ></td>
                        </tr>
                        <tr>
                          <td>Redirect URL</td>
                          <td
                            class="text-right"
                            id="makePaymentResultRedirectURL"
                          ></td>
                        </tr>
                        <tr>
                          <td>Image</td>
                          <td
                            class="text-right"
                            id="makePaymentResultImage"
                          ></td>
                        </tr>
                        <tr>
                          <td>Timeout</td>
                          <td
                            class="text-right"
                            id="makePaymentResultTimeout"
                          ></td>
                        </tr>
                      </tbody>
                    </table>

                    <button
                      type="button"
                      class="btn btn-danger"
                      data-bs-dismiss="modal"
                    >
                      <i class="mdi mdi-close-thick"></i> Close
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div
          class="modal fade"
          id="modalMakeWithdraw"
          data-bs-backdrop="static"
          data-bs-keyboard="false"
          tabindex="-1"
          aria-labelledby="modalMakeWithdraw"
          aria-modal="true"
        >
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">
                  Make Withdraw
                  <span class="text-danger fs-14"
                    >(ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)</span
                  >
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <form
                  action="javascript:void(0);"
                  id="formMakeWithdraw"
                  enctype="multipart/form-data"
                >
                  <div class="row g-3">
                    <div class="col-md-6">
                      <div>
                        <label for="makeWithdrawOrderId" class="form-label"
                          >Order ID <span class="text-danger">*</span></label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="makeWithdrawOrderId"
                          id="makeWithdrawOrderId"
                          required
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="makeWithdrawAmount" class="form-label"
                          >Amount (Limit 500,000)<span class="text-danger"
                            >*</span
                          ></label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="makeWithdrawAmount"
                          onchange="calMDR(0.00);"
                          onkeyup="calMDR(0.00);"
                          id="makeWithdrawAmount"
                          required
                          placeholder="100.00"
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="makeWithdrawMDR" class="form-label"
                          >MDR
                          <span class="text-danger" id="makeWithdrawMDRAmount"
                            >0.00%</span
                          >
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          name="makeWithdrawMDR"
                          id="makeWithdrawMDR"
                          readonly
                          style="background: #f0f0f0; color: #000"
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="makeWithdrawDeductBalance"
                          class="form-label"
                          >Deduct Balance
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          name="makeWithdrawDeductBalance"
                          id="makeWithdrawDeductBalance"
                          readonly
                          style="background: #f0f0f0; color: #000"
                        />
                      </div>
                    </div>
                    <!--end col-->

                    <div class="col-md-6">
                      <div>
                        <label
                          for="makeWithdrawBankAccountName"
                          class="form-label"
                          >Bank Account Name
                          <span class="text-danger requireTransfer">*</span>
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          name="makeWithdrawBankAccountName"
                          id="makeWithdrawBankAccountName"
                          placeholder="สมชาย ใจดี"
                          required
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="makeWithdrawBankAccountNo"
                          class="form-label"
                          >Bank Account No
                          <span class="text-danger requireTransfer">*</span>
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          name="makeWithdrawBankAccountNo"
                          id="makeWithdrawBankAccountNo"
                          maxlength="13"
                          placeholder="xxxxxxxxx"
                          required
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-12">
                      <div>
                        <label for="makeWithdrawBankName" class="form-label"
                          >Bank Name
                          <span class="text-danger requireTransfer">*</span>
                        </label>
                        <select
                          name="makeWithdrawBankName"
                          id="makeWithdrawBankName"
                          class="form-select"
                        >
                          <option value="">Please select bank</option>
                          <option value="034">
                            BAAC-ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร
                          </option>
                          <option value="025">BAY-ธนาคารกรุงศรีอยุธยา</option>
                          <option value="002">BBL-ธนาคารกรุงเทพ</option>
                          <option value="901">CDM-โอนเงินจากตู้ ATM</option>
                          <option value="022">CIMB-ธนาคารซีไอเอ็มบีไทย</option>
                          <option value="017">CITI-ซิตี้แบงก์</option>
                          <option value="033">
                            GHB-ธนาคารอาคารสงเคราะห์ (ธอส.)
                          </option>
                          <option value="030">GSB-ธนาคารออมสิน</option>
                          <option value="066">ISBT-ธนาคารอิสลาม</option>
                          <option value="004">KBANK-ธนาคารกสิกรไทย</option>
                          <option value="069">KKP- ธนาคาร เกียรตินาคิน</option>
                          <option value="006">KTB-ธนาคารกรุงไทย</option>
                          <option value="073">
                            LHBANK-ธนาคารแลนด์ แอนด์ เฮาส์
                          </option>
                          <option value="014">SCB-ไทยพาณิชย์</option>
                          <option value="018">
                            SMBC-ธนาคาร ซูมิโตโม มิตซุย แบงกิ้ง
                          </option>
                          <option value="071">
                            TCRB-ธนาคารไทยเครดิต เพื่อรายย่อย
                          </option>
                          <option value="067">TISCO-ธนาคารทิสโก้</option>
                          <option value="801">TRUE-True Wallet</option>
                          <option value="011">TTB-ธนาคารทหารไทยธนชาติ</option>
                          <option value="024">UOB-ธนาคารยูโอบี</option>
                        </select>
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-12 d-none" id="require_pin_code">
                      <div>
                        <label for="makeWithdrawOrderId" class="form-label"
                          >Pin Code <span class="text-danger">*</span></label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="makeWithdrawPinCode"
                          required
                          maxlength="6"
                          minlength="6"
                          autocomplete="off"
                        />
                        <input
                          type="hidden"
                          class="form-control"
                          name="makeWithdrawToken"
                          id="makeWithdrawToken"
                          required
                        />
                      </div>
                      <div
                        class="row g-3 mt-2 d-none"
                        id="makeWithdrawPinAlert"
                      >
                        <div
                          class="col-md-12 text-danger text-center"
                          id="makeWithdrawPinAlertText"
                        ></div>
                        <!--end col-->
                      </div>
                      <!--end row-->
                    </div>
                    <!--end col-->
                  </div>
                  <div class="row g-3 mt-2" id="makeWithdrawSubmit">
                    <div class="col-md-12">
                      <div class="hstack gap-2 justify-content-center">
                        <button
                          type="submit"
                          class="btn btn-danger"
                          onclick="return confirmMakeWithdraw();"
                        >
                          <i class="mdi mdi-transfer"></i> Submit
                        </button>
                        <button
                          type="button"
                          class="btn btn-light"
                          data-bs-dismiss="modal"
                        >
                          <i class="mdi mdi-close-thick"></i> Cancel
                        </button>
                      </div>
                      <br />
                      <span
                        >ถอนได้ครั้งละ 500,000 บาทต่อรายการ
                        สามารถถอนหลายรายการพร้อมกันได้เช่น ต้องการถอนเงิน 1.3
                        ล้าน ทำให้การแบ่งรายการถอนเป็น 5 แสนบาท 2 ครั้งและ 3
                        แสนบาท 1 ครั้ง</span
                      >
                    </div>
                    <!--end col-->
                  </div>
                  <!--end row-->
                  <div class="row g-3 mt-2 d-none" id="makeWithdrawSubmitalert">
                    <div class="col-md-12 text-danger text-center">
                      วงเงินสำหรับถอนไม่เพียงพอต่อการถอน
                    </div>
                    <!--end col-->
                  </div>
                  <!--end row-->
                </form>
              </div>
            </div>
          </div>
        </div>

        <div
          class="modal fade"
          id="modalConfirmStatement"
          data-bs-backdrop="static"
          data-bs-keyboard="false"
          tabindex="-1"
          aria-labelledby="modalConfirmStatement"
          aria-modal="true"
        >
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">
                  Confirm Transaction
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <form action="javascript:void(0);" id="formConfirmTransaction">
                  <input
                    type="hidden"
                    name="confirmTransactionStatementID"
                    id="confirmTransactionStatementID"
                    class="form-control"
                    value=""
                  />
                  <div class="row g-3">
                    <div class="col-md-6">
                      <div>
                        <label
                          for="confirmTransactionTransactionDate"
                          class="form-label"
                          >Transaction Date</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="confirmTransactionTransactionDate"
                          id="confirmTransactionTransactionDate"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="confirmTransactionOrderID"
                          class="form-label"
                          >Order ID</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="confirmTransactionOrderID"
                          id="confirmTransactionOrderID"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="confirmTransactionFromAccount"
                          class="form-label"
                          >From Account</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="confirmTransactionFromAccount"
                          id="confirmTransactionFromAccount"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label for="confirmTransactionAmount" class="form-label"
                          >Amount</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="confirmTransactionAmount"
                          id="confirmTransactionAmount"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-12">
                      <div>
                        <label for="confirmTransactionRemark" class="form-label"
                          >Remark</label
                        >
                        <textarea
                          class="form-control"
                          name="confirmTransactionRemark"
                          id="confirmTransactionRemark"
                          rows="3"
                          disabled
                        ></textarea>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <div class="row g-3 mt-2" id="makeWithdrawSubmit">
                    <div class="col-md-6">
                      <div class="hstack gap-2 justify-content-start">
                        <button
                          type="submit"
                          class="btn btn-danger"
                          onclick="return refuseConfirmTransaction();"
                        >
                          <i class="mdi mdi-close-thick"></i> Refuse
                        </button>
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div class="hstack gap-2 justify-content-end">
                        <button
                          type="submit"
                          class="btn btn-success"
                          onclick="return confirmConfirmTransaction();"
                        >
                          <i class="mdi mdi-check-bold"></i> Confirm
                        </button>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <!--end row-->
                </form>
              </div>
            </div>
          </div>
        </div>

        <div
          id="modalSlip"
          class="modal fade"
          tabindex="-1"
          aria-labelledby="modalSlipLabel"
          aria-hidden="true"
          style="display: none"
        >
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="modalSlipLabel">
                  Slip Transfer <span id="dateEmail"></span>
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <div id="htmlEmail"></div>
              </div>
              <div class="modal-footer">
                <button
                  type="button"
                  class="btn btn-light"
                  data-bs-dismiss="modal"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          class="modal fade"
          id="modalReportWithdrawProblem"
          data-bs-backdrop="static"
          data-bs-keyboard="false"
          tabindex="-1"
          aria-labelledby="modalReportWithdrawProblem"
          aria-modal="true"
        >
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">
                  Report Withdraw Problem
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <form
                  action="javascript:void(0);"
                  id="formReportWithdrawProblem"
                >
                  <input
                    type="hidden"
                    name="reportWithdrawProblemTransactionID"
                    id="reportWithdrawProblemTransactionID"
                    class="form-control"
                    value=""
                  />
                  <div class="row g-3">
                    <div class="col-md-6">
                      <div>
                        <label
                          for="reportWithdrawProblemTransactionDate"
                          class="form-label"
                          >Transaction Date</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="reportWithdrawProblemTransactionDate"
                          id="reportWithdrawProblemTransactionDate"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="reportWithdrawProblemOrderID"
                          class="form-label"
                          >Order ID</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="reportWithdrawProblemOrderID"
                          id="reportWithdrawProblemOrderID"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="reportWithdrawProblemToAccount"
                          class="form-label"
                          >To Account</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="reportWithdrawProblemToAccount"
                          id="reportWithdrawProblemToAccount"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div>
                        <label
                          for="reportWithdrawProblemAmount"
                          class="form-label"
                          >Amount</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="reportWithdrawProblemAmount"
                          id="reportWithdrawProblemAmount"
                          disabled
                        />
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-12">
                      <div>
                        <label
                          for="reportWithdrawProblemType"
                          class="form-label"
                          >Type <span class="text-danger">*</span></label
                        >
                        <select
                          id="reportWithdrawProblemType"
                          class="form-select"
                        ></select>
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-12">
                      <div>
                        <label
                          for="reportWithdrawProblemRemark"
                          class="form-label"
                          >Remark</label
                        >
                        <textarea
                          class="form-control"
                          name="reportWithdrawProblemRemark"
                          id="reportWithdrawProblemRemark"
                          maxlength="200"
                          rows="3"
                        ></textarea>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <div class="row g-3 mt-2" id="makeWithdrawSubmit">
                    <div class="col-md-6">
                      <div class="hstack gap-2 justify-content-start">
                        <button
                          type="button"
                          class="btn btn-light"
                          data-bs-dismiss="modal"
                        >
                          <i class="mdi mdi-close-thick"></i> Close
                        </button>
                      </div>
                    </div>
                    <!--end col-->
                    <div class="col-md-6">
                      <div class="hstack gap-2 justify-content-end">
                        <button
                          type="submit"
                          class="btn btn-danger"
                          onclick="return confirmReportWithdrawProblem();"
                        >
                          <i class="mdi mdi-check-bold"></i> Report
                        </button>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <!--end row-->
                </form>
              </div>
            </div>
          </div>
        </div>
        <footer class="footer">
          <div class="container-fluid">
            <div class="row">
              <div class="col-sm-6">
                Copyright
                <script>
                  document.write(new Date().getFullYear());
                </script>
                © member.sugarpay66.io
              </div>
              <div class="col-sm-6"></div>
            </div>
          </div>
        </footer>
      </div>
      <!-- end main content-->
    </div>
    <!-- END layout-wrapper -->

    <!--start back-to-top-->
    <button
      onclick="topFunction()"
      class="btn btn-primary btn-icon"
      id="back-to-top"
    >
      <i class="ri-arrow-up-line"></i>
    </button>
    <!--end back-to-top-->

    <!--preloader-->
    <div id="preloader">
      <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>

    <div
      class="modal fade"
      id="modelPreviewImage"
      tabindex="-1"
      aria-labelledby="modelPreviewImage"
      aria-modal="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalgridLabel">
              Preview Image
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <img src="" id="imgPreviewImage" style="width: 100%" alt="" />
          </div>
        </div>
      </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="public/assets/libs/simplebar/simplebar.min.js"></script>
    <script src="public/assets/libs/node-waves/waves.min.js"></script>
    <script src="public/assets/libs/feather-icons/feather.min.js"></script>
    <script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
    <script src="public/assets/js/plugins.js?date=202403042044"></script>

    <script src="public/assets/js/pages/notifications.init.js"></script>

    <script
      src="https://code.jquery.com/jquery-3.6.0.min.js"
      integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="
      crossorigin="anonymous"
    ></script>

    <!--datatable js-->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

    <!-- Sweet Alerts js -->
    <script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

    <script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>

    <!-- Sweet alert init js-->
    <script src="public/assets/js/pages/sweetalerts.init.js"></script>

    <!-- Moment js -->
    <script src="public/assets/libs/moment/moment.js"></script>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"
      integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js"
      integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

    <!-- dropzone min -->
    <script src="public/assets/libs/dropzone/dropzone-min.js"></script>
    <!-- filepond js -->
    <script src="public/assets/libs/filepond/filepond.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

    <!-- App js -->
    <script src="public/assets/js/app.js?t=1746205200"></script>
    <script>
      var tableDeposit = {
        table: "",
        tableDrawStatus: true,
        tooltipTriggerList: "",
        tooltipList: "",
      };
      document.addEventListener("DOMContentLoaded", function () {
        new DataTable(".alternative-pagination", {});
      });
      $(document).ready(function () {});

      function unshowAnnouncement(id) {
        setCookie("cookie_announcement", id, 7);
        $("#modalAnnouncement").modal("hide");
      }

      function setCookie(name, value, days) {
        var expires = "";
        if (days) {
          var date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
      }
      function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(";");
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) === " ") c = c.substring(1, c.length);
          if (c.indexOf(nameEQ) === 0)
            return c.substring(nameEQ.length, c.length);
        }
        return null;
      }

      $(document).on("click", "img.previewImage", function () {
        let src = $(this).attr("src");
        $("#imgPreviewImage").attr("src", src);

        $("#modelPreviewImage").modal("show");
      });

      function formatNumber(value, digit = 2) {
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
          maximumFractionDigits: digit,
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      }
      function preview_image(event, obj) {
        var output = document.getElementById("show_" + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
      }

      function copyButton(elm, copyText, afterTextButton) {
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
      }
      function copyToClipboard(text) {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(text).then(
            function () {
              console.log("Text copied to clipboard");
              Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Text copied to clipboard",
                showConfirmButton: false,
                timer: 1500,
              });
            },
            function (err) {
              console.error("Could not copy text: ", err);
            }
          );
        } else {
          let input = document.createElement("textarea");
          input.style.position = "fixed";
          input.style.zIndex = 9999;
          input.value = text;
          document.body.appendChild(input);
          input.select();
          input.focus();
          document.execCommand("copy");
          document.body.removeChild(input);
        }
      }
    </script>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.2.0/crypto-js.min.js"
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script
      lang="javascript"
      src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"
    ></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.js"></script>
    <script>
      var url_ajax = "/transaction/";
      let start_date = moment()
        .add(-0, "day")
        .format("YYYY-MM-DD 00:00")
        .toString();
      let end_date = moment().format("YYYY-MM-DD 23:59").toString();
      let table = "";
      let tableDrawStatus = true;
      let tooltipTriggerList = "";
      let tooltipList = "";
      let total_withdraw_balance = 0;
      let minimum_2fa = 1000;
      $(document).ready(function () {
        $("#start_date").flatpickr({
          enableTime: true,
          dateFormat: "Y-m-d H:i",
          time_24hr: true,
          defaultDate: [start_date],
          onChange: function (selectedDates, dateStr, instance) {
            start_date = dateStr;
          },
        });
        $("#end_date").flatpickr({
          enableTime: true,
          dateFormat: "Y-m-d H:i",
          time_24hr: true,
          defaultDate: [end_date],
          onChange: function (selectedDates, dateStr, instance) {
            end_date = dateStr;
          },
        });
        get_total_box_balance();
        get_datatable_list();
      });
      $(".number-only").on("input", function () {
        this.value = this.value.replace(/[^0-9.]/g, "");
      });

      function get_total_box_balance() {
        $.ajax({
          url: url_ajax + "getTotalBoxBalance",
          type: "post",
          async: false,
          data: {},
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              let deposit_balance =
                res.result.deposit_balance != null
                  ? res.result.deposit_balance
                  : 0;
              let withdraw_balance =
                res.result.withdraw_balance != null
                  ? res.result.withdraw_balance
                  : 0;
              total_withdraw_balance = withdraw_balance;
              $("#total_balance_amount").html(formatNumber(deposit_balance));
              $("#total_withdraw_balance").html(formatNumber(withdraw_balance));

              $("#total_balance_amount").attr("data-target", deposit_balance);
              $("#total_withdraw_balance").attr(
                "data-target",
                withdraw_balance
              );
            } else {
              $("#total_balance_amount").html(0);
              $("#total_withdraw_balance").html(0);

              $("#total_balance_amount").attr("data-target", 0);
              $("#total_withdraw_balance").attr("data-target", 0);
            }
          },
        });
      }

      function get_datatable_list() {
        var text_search = $("#text_search").val().trim();
        var status_search = $("#status_search").val().trim();
        var channel_search = $("#channel_search").val().trim();
        var transaction_type_search = $("#transaction_type_search")
          .val()
          .trim();
        var deposit_type_search = $("#deposit_type_search").val().trim();

        var transaction_bank_acc_no = $("#transaction_bank_acc_no")
          .val()
          .trim();

        var from_txn_amount_search = $("#from_txn_amount_search").val().trim();
        var to_txn_amount_search = $("#to_txn_amount_search").val().trim();

        $.fn.dataTable.ext.errMode = "none";
        table = $("#datatable-list").DataTable({
          // data: response.result,
          destroy: true,
          processing: true,
          serverSide: true,
          searchDelay: 500,
          pageLength: 50,
          ajax: {
            url: url_ajax + "getDatatableList",
            type: "POST",
            data: {
              text_search: text_search,
              status_search: status_search,
              channel_search: channel_search,
              transaction_type_search: transaction_type_search,
              deposit_type_search: deposit_type_search,
              transaction_bank_acc_no: transaction_bank_acc_no,
              start_date: start_date + ":00",
              end_date: end_date + ":59",
              from_txn_amount_search: from_txn_amount_search,
              to_txn_amount_search: to_txn_amount_search,
            },
            dataSrc: function (response) {
              console.log("response", response);
              $("#total_deposit_amount").text(
                formatNumber(response.summary.txn_amount, 2)
              );
              $("#total_mdr_amount").text(
                formatNumber(response.summary.mdr_amount, 2)
              );
              $("#total_net_amount").text(
                formatNumber(response.summary.net_amount, 2)
              );
              return response.data;
            },
          },
          columnDefs: [
            {
              targets: [4, 5, 6],
              className: "text-right",
            },
            // ,{
            //     targets: [],
            //     className: 'text-center'
            // }
          ],
          columns: [
            // {data: function(data, type, dataToSet) { return moment(data.created_date).format("YYYY-MM-DD HH:mm:ss"); }},
            {
              data: function (data, type, dataToSet) {
                var txnDate = "";
                if (data.txn_date == null) {
                  txnDate = "";
                } else {
                  txnDate = `<br />${moment(data.txn_date).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )}`;
                }
                return `${moment(data.created_date).format(
                  "YYYY-MM-DD HH:mm:ss"
                )} ${txnDate}`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                return data.merchant_name;
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (data.order_id == null && data.customer_username == null) {
                  return "";
                }
                if (data.slip_image != "" && data.slip_image != null) {
                  return `<span onClick="preview_image('/public/uploads/move_to_agent/${
                    data.slip_image
                  }')">${
                    data.order_id
                  } <i class="text-success ri-image-2-fill fs-4" ></i></span><br />${
                    data.customer_username ?? ""
                  }`;
                }
                return `<span style="color:dark; cursor:pointer" onClick="copyToClipboard('${
                  data.order_id
                }')">${
                  data.order_id ? truncateText(data.order_id, 50) : "-"
                }</span> <br />${data.customer_username ?? ""}`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                return data.transaction_type;
              },
            },
            {
              data: function (data, type, dataToSet) {
                return formatNumber(data.amount, 2);
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (
                  data.transaction_type == "WITHDRAW" &&
                  data.status == "SUCCESS"
                ) {
                  return formatNumber(
                    parseFloat(data.mdr_amount) +
                      parseFloat(data.withdraw_fee_amount),
                    2
                  );
                } else {
                  return formatNumber(parseFloat(data.mdr_amount ?? 0), 2);
                }
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (
                  data.transaction_type == "WITHDRAW" &&
                  data.status == "SUCCESS"
                ) {
                  return formatNumber(
                    parseFloat(data.net_amount) +
                      parseFloat(data.withdraw_fee_amount),
                    2
                  );
                } else {
                  return formatNumber(parseFloat(data.net_amount ?? 0), 2);
                }
              },
            },
            {
              data: function (data, type, dataToSet) {
                var btnSlip = "";
                if (data.withdraw_ref_no != null) {
                  const instructionRefNo = data.withdraw_ref_no;
                  btnSlip = `<br /><span class="mt-2" style="text-decoration: underline; font-weight:bold; color:blue; cursor:pointer;"  onClick="showModalSlip('${instructionRefNo}')">ดูสลิป</span>`;
                }
                let btnResendCallback = ``;
                btnResendCallback = `<a href="javascript:void(0)" class="badge text-bg-info mouseOverlay" onclick="confirmResendCallback(${data.transaction_id});"><i class="mdi mdi-cursor-default-click-outline"></i> Callback</a>`;
                if (data.status == "SUCCESS") {
                  let btnReport = ``;

                  if (data.transaction_type == "WITHDRAW") {
                    if (data.is_withdraw_problem == 0) {
                      btnReport = `<a href="javascript:void(0)" class="badge text-bg-danger mouseOverlay" onclick="reportWithdrawProblem(${data.transaction_id});"><i class="mdi mdi-alert-outline"></i> แจ้งปัญหา</a>`;
                    } else if (data.is_withdraw_problem == 1) {
                      btnReport = `<a href="javascript:void(0)" class="badge text-bg-warning " ><i class="mdi mdi-alert-outline"></i> รอตรวจสอบปัญหา</a>`;
                    } else if (data.is_withdraw_problem == 2) {
                      btnReport = `<a href="javascript:void(0)" class="badge text-bg-primary"><i class="mdi mdi-alert-outline"></i> ยกเลิกแล้ว</a>`;
                    } else if (data.is_withdraw_problem == 3) {
                      btnReport = `<a href="javascript:void(0)" class="badge text-bg-primary"><i class="mdi mdi-check-circle-outline"></i> ยืนยันโอนแล้ว</a>`;
                    }
                  }
                  return `<span class="badge text-bg-success">${data.status}</span> ${btnReport} ${btnResendCallback} ${btnSlip}`;
                } else if (data.status == "WAIT_CONFIRM") {
                  btnResendCallback = ``;
                  return `<button class="btn btn-sm btn-warning " onclick="confirmTransaction(${data.statement_id});"><i class="mdi mdi-checkbox-marked-outline"></i> ${data.status}</button> ${btnSlip}`;
                } else if (
                  data.status == "REFUSE" ||
                  data.status == "BLACKLIST"
                ) {
                  btnResendCallback = ``;
                  return `<span class="badge text-bg-dark">${data.status}</span> ${btnResendCallback} ${btnSlip}`;
                } else if (data.status == "FAILED") {
                  let remark = ``;
                  if (data.code == "CUST_CREATE_NEW_QR") {
                    remark = `<span class="badge text-bg-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="ยกเลิกจากการสร้าง QR จากร้านค้าอื่นด้วยเลขบัญชีลูกค้าเดียวกัน"  ><i class="mdi mdi-information-variant"></i></span>`;
                  }
                  let btnReport = ``;
                  if (data.transaction_type == "WITHDRAW") {
                    if (data.is_withdraw_problem == 2) {
                      btnReport = `<a href="javascript:void(0)" class="badge text-bg-primary"><i class="mdi mdi-alert-outline"></i> ยกเลิกแล้ว</a>`;
                    } else if (data.is_withdraw_problem == 3) {
                      btnReport = `<a href="javascript:void(0)" class="badge text-bg-primary"><i class="mdi mdi-check-circle-outline"></i> ยืนยันโอนแล้ว</a>`;
                    }
                  }
                  let cancel = ``;
                  if (data.code == "CANCEL") {
                    cancel = `- CANCEL`;
                  }

                  return `<span class="badge text-bg-danger">${data.status}${cancel}</span> ${btnReport} ${btnResendCallback} ${remark} ${btnSlip}`;
                } else {
                  let status = data.status;
                  if (data.status == "CREATE") {
                    const instructionRefNo = data.withdraw_ref_no;
                    if (data.transaction_type == "WITHDRAW") {
                      if (data.transfer_group_id != null) {
                        return `<span class="badge text-bg-warning" >ON_PROCESS</span>`;
                      } else if (data.withdraw_ref_no == null) {
                        let btnCancel = ``;
                        btnCancel = ` <a href="javascript:void(0)" class="badge text-bg-danger" onclick="confirmCancelWithdraw(${data.transaction_id});"><i class="mdi mdi-cursor-default-click-outline"></i> Cancel Withdraw</a>`;

                        status = "CREATE";
                        if (data.txn_status == "PENDING_CHECKSUM") {
                          status = "WAIT_APPROVE";
                        }

                        return `<span class="badge text-bg-light" >${status}</span>${btnCancel}`;
                      } else {
                        return `<span class="badge text-bg-secondary" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-html="true" data-bs-title="ระบบได้ดำเนินการส่งไปยังธนาคารเรียบร้อยเเล้ว">${instructionRefNo}</span>`;
                      }

                      if (data.txn_status == "PENDING_CHECKSUM") {
                        status = "WAIT_APPROVE";
                      }
                    }
                  }
                  return `<span class="badge text-bg-light">${status}</span> ${btnSlip}`;
                }
              },
            },
            {
              data: function (data, type, dataToSet) {
                return `${data.bank_name ?? ""} - ${
                  data.bank_acc_no ?? ""
                } <br />${data.bank_acc_name ?? ""}`;
              },
            },
          ],
          order: [[0, "desc"]],
        });

        tableDraw();
      }

      function tableDraw() {
        if (tableDrawStatus) {
          table.on("draw", function () {
            tooltipTriggerList = document.querySelectorAll(
              '[data-bs-toggle="tooltip"]'
            );
            tooltipList = [...tooltipTriggerList].map(
              (tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl)
            );
          });
          tableDrawStatus = false;
        }
      }
      function truncateText(text, maxLength) {
        if (text != null && text.length > maxLength) {
          return text.substring(0, maxLength) + "...";
        }
        return text;
      }

      $(
        "#makePaymentAmount,#makePaymentBankAccountNo,#makeWithdrawAmount,#makeWithdrawBankAccountNo"
      ).on("keyup", function (e) {
        var val = $(this).val();
        val = val.replace(/[^\d.]/g, ""); // Remove all non-numeric characters
        $(this).val(val);
      });

      function makePayment() {
        $("#formMakePayment")[0].reset();
        $("#modalMakePayment").modal("show");
      }

      function makeWithdraw() {
        $("#formMakeWithdraw")[0].reset();

        $.ajax({
          url: url_ajax + "setup_transaction_withdraw",
          type: "post",
          async: false,
          data: {},
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              minimum_2fa = res.result.minimum_2fa;
              $("#modalMakeWithdraw").modal("show");
            } else if (res.error == "2") {
              Swal.fire({
                icon: "warning",
                title: `Your merchant isn't setup pin code. please use master account and go to profile page.`,
                confirmButtonText: "Close",
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: `Can't make withdraw`,
                confirmButtonText: "Close",
              });
            }
          },
        });
      }

      function confirmMakePayment() {
        let amount = parseFloat($("#makePaymentAmount").val());
        let order_id = $("#makePaymentOrderId").val();
        if (amount <= 0 || amount == null || amount == "" || order_id == "") {
          Swal.fire({
            icon: "warning",
            title: `Please input data was required.`,
            confirmButtonText: "Close",
          });

          return false;
        }

        Swal.fire({
          title: "Confirm",
          text: "You want to make transaction payment, right?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveMakePayment();
          }
        });
      }

      $("#makePaymentType").change(function () {
        let val = $(this).val();
        if (val == "TRANSFER") {
          $(".requireTransfer").removeClass("d-none");
        } else {
          $(".requireTransfer").addClass("d-none");
        }
      });

      function preview_image(src) {
        $("#imgPreviewImage").attr("src", src);
        $("#modelPreviewImage").modal("show");
      }

      function saveMakePayment() {
        let amount = parseFloat($("#makePaymentAmount").val());
        let order_id = $("#makePaymentOrderId").val();
        if (amount <= 0 || amount == null || amount == "" || order_id == "") {
          Swal.fire({
            icon: "warning",
            title: `Please fill order id or amount.`,
            confirmButtonText: "Close",
          });

          return false;
        } else {
          const formData = new FormData($("#formMakePayment")[0]);

          $("#makePaymentType").attr("disabled", "disabled");
          $("#makePaymentOrderId").attr("disabled", "disabled");
          $("#makePaymentAmount").attr("disabled", "disabled");
          $("#makePaymentBankAccountName").attr("disabled", "disabled");
          $("#makePaymentBankAccountNo").attr("disabled", "disabled");
          $("#makePaymentBankName").attr("disabled", "disabled");

          $.ajax({
            url: url_ajax + "make_payment",
            type: "post",
            async: false,
            data: formData,
            contentType: false,
            processData: false,
            dataType: "json",
            success: function (res) {
              if (res.error == "0" && res.code == "200") {
                $("#makePaymentSubmit").hide();
                $("#makePaymentResult").show();

                let redirect_url = "-";
                if (res.result.redirect_url != "") {
                  redirect_url = `<button class="btn btn-sm btn-warning mx-1" onclick="copyButton(this, '${res.result.redirect_url}', 'Copied!!')">Copy Link</button> `;
                  redirect_url += `<a href="${res.result.redirect_url}" class="btn btn-sm btn-info" target="_blank">Open Link.</a>`;
                }

                let imageQrcode = "-";
                if (res.result.image != undefined && res.result.image != "") {
                  imageQrcode = `<button class="btn btn-sm btn-warning" onclick="copyButton(this, '${res.result.image}', 'Copied!!')">Copy Link Image</button><br>`;
                  imageQrcode += `<img src="${res.result.image}" style="width: 100%; max-width: 200px;">`;
                }

                let timeOut = moment(res.result.timeout.date)
                  .format("YYYY-MM-DD HH:mm:ss")
                  .toString();

                $("#makePaymentResultRefId").html(`${res.result.ref_id}`);
                $("#makePaymentResultOrderId").html(`${res.result.order_id}`);
                $("#makePaymentResultPrice").html(
                  `${formatNumber(res.result.price, 2)}`
                );
                $("#makePaymentResultRedirectURL").html(`${redirect_url}`);
                $("#makePaymentResultImage").html(`${imageQrcode}`);
                $("#makePaymentResultTimeout").html(`${timeOut}`);
              } else {
                $("#makePaymentType").removeAttr("disabled");
                $("#makePaymentOrderId").removeAttr("disabled");
                $("#makePaymentAmount").removeAttr("disabled");
                $("#makePaymentBankAccountName").removeAttr("disabled");
                $("#makePaymentBankAccountNo").removeAttr("disabled");
                $("#makePaymentBankName").removeAttr("disabled");

                $("#makePaymentSubmit").show();
                $("#makePaymentResult").hide();

                Swal.fire({
                  icon: "warning",
                  title: res.message,
                  confirmButtonText: "Close",
                }).then((result) => {});
              }
            },
          });
        }

        return false;
      }

      $("#modalMakePayment,#modalMakeWithdraw").on(
        "hidden.bs.modal",
        function (e) {
          window.location.reload();
        }
      );

      function calMDR(mdr_rate) {
        mdr_rate = parseFloat(mdr_rate);
        let amount = parseFloat($("#makeWithdrawAmount").val());
        let deduct_amount = amount / (1 - mdr_rate / 100);
        let mdr_amount = deduct_amount - amount;

        $("#makeWithdrawMDR").val(formatNumber(mdr_amount, 2));
        $("#makeWithdrawDeductBalance").val(formatNumber(deduct_amount, 2));

        if (deduct_amount > total_withdraw_balance) {
          $("#makeWithdrawSubmitalert").removeClass("d-none");
          $("#makeWithdrawSubmit").addClass("d-none");
        } else {
          $("#makeWithdrawSubmit").removeClass("d-none");
          $("#makeWithdrawSubmitalert").addClass("d-none");
        }

        if (minimum_2fa <= amount) {
          $("#require_pin_code").removeClass("d-none");
        } else {
          $("#require_pin_code").addClass("d-none");
        }
      }

      function confirmMakeWithdraw() {
        let amount = parseFloat($("#makeWithdrawAmount").val());
        let order_id = $("#makeWithdrawOrderId").val();
        if (amount <= 0 || amount == null || amount == "" || order_id == "") {
          Swal.fire({
            icon: "warning",
            title: `Please input data was required.`,
            confirmButtonText: "Close",
          });

          return false;
        }

        if (amount > 500000.0) {
          Swal.fire({
            icon: "warning",
            title: `Withdraw limit 500,000 per transaction.`,
            confirmButtonText: "Close",
          });

          return false;
        }

        Swal.fire({
          title: "Confirm",
          text: "You want to make transaction withdraw, right?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            setupTransactionWithdraw();
          }
        });
      }

      function isValidPin(pin) {
        return /^\d+$/.test(pin);
      }
      var CryptoJSAesJson = {
        stringify: function (cipherParams) {
          var j = { ct: cipherParams.ciphertext.toString(CryptoJS.enc.Base64) };
          if (cipherParams.iv) j.iv = cipherParams.iv.toString();
          if (cipherParams.salt) j.s = cipherParams.salt.toString();
          return JSON.stringify(j);
        },
        parse: function (jsonStr) {
          var j = JSON.parse(jsonStr);
          var cipherParams = CryptoJS.lib.CipherParams.create({
            ciphertext: CryptoJS.enc.Base64.parse(j.ct),
          });
          if (j.iv) cipherParams.iv = CryptoJS.enc.Hex.parse(j.iv);
          if (j.s) cipherParams.salt = CryptoJS.enc.Hex.parse(j.s);
          return cipherParams;
        },
      };
      function setupTransactionWithdraw() {
        let amount = parseFloat($("#makeWithdrawAmount").val());
        let order_id = $("#makeWithdrawOrderId").val();

        $.ajax({
          url: url_ajax + "setup_transaction_withdraw",
          type: "post",
          async: false,
          data: {
            amount: amount,
            order_id: order_id,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              if (amount >= res.result.minimum_2fa) {
                let pin_code = $("#makeWithdrawPinCode").val().trim();
                let code = CryptoJS.AES.encrypt(
                  JSON.stringify(pin_code),
                  CryptoJS.MD5(res.result.token).toString(),
                  { format: CryptoJSAesJson }
                ).toString();
                $("#makeWithdrawToken").val(code);
                saveMakeWithdraw();
              } else {
                $("#require_pin_code").addClass("d-none");
                let code = CryptoJS.AES.encrypt(
                  JSON.stringify(order_id),
                  CryptoJS.MD5(res.result.token).toString(),
                  { format: CryptoJSAesJson }
                ).toString();
                $("#makeWithdrawToken").val(code);
                saveMakeWithdraw();
              }
            } else if (res.error == "2") {
              Swal.fire({
                icon: "warning",
                title: `Your merchant isn't setup pin code. please use master account and go to profile page.`,
                confirmButtonText: "Close",
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: `Can't make withdraw`,
                confirmButtonText: "Close",
              });
            }
          },
        });

        return false;
      }

      function saveMakeWithdraw() {
        let amount = parseFloat($("#makeWithdrawAmount").val());
        let order_id = $("#makeWithdrawOrderId").val();
        if (amount <= 0 || amount == null || amount == "" || order_id == "") {
          Swal.fire({
            icon: "warning",
            title: `Please fill order id or amount.`,
            confirmButtonText: "Close",
          });

          return false;
        } else {
          const formData = new FormData($("#formMakeWithdraw")[0]);

          $("#makeWithdrawOrderId").attr("disabled", "disabled");
          $("#makeWithdrawAmount").attr("disabled", "disabled");
          $("#makeWithdrawBankAccountName").attr("disabled", "disabled");
          $("#makeWithdrawBankAccountNo").attr("disabled", "disabled");
          $("#makeWithdrawBankName").attr("disabled", "disabled");

          $.ajax({
            url: url_ajax + "make_withdraw",
            type: "post",
            async: false,
            data: formData,
            contentType: false,
            processData: false,
            dataType: "json",
            success: function (res) {
              if (res.error == "0" && res.code == "200") {
                Swal.fire({
                  icon: "success",
                  title: res.message,
                  confirmButtonText: "Close",
                }).then((result) => {
                  window.location.reload();
                });
              } else if (res.error == "2") {
                $("#makeWithdrawPinCode").val("");
                $("#makeWithdrawToken").val("");

                $("#makeWithdrawOrderId").removeAttr("disabled");
                $("#makeWithdrawAmount").removeAttr("disabled");
                $("#makeWithdrawBankAccountName").removeAttr("disabled");
                $("#makeWithdrawBankAccountNo").removeAttr("disabled");
                $("#makeWithdrawBankName").removeAttr("disabled");
                Swal.fire({
                  icon: "warning",
                  title: `Pin Code ไม่ถูกต้อง`,
                  confirmButtonText: "Close",
                }).then((result) => {
                  $("#makeWithdrawPinCode").focus();
                  $("#makeWithdrawPinAlert").removeClass("d-none");
                  $("#makeWithdrawPinAlertText").text(
                    `เหลือสิทธิ์ในการกรอก pin code อีก ${res.result.try_amount} ครั้ง`
                  );
                });
              } else if (res.error == "3") {
                $("#makeWithdrawPinCode").val("");
                $("#makeWithdrawToken").val("");

                Swal.fire({
                  icon: "warning",
                  title: `คุณกรอก Pin Code ผิดเกินจำนวนครั้งที่กำหนด. Pin code lock กรุณาติดต่อแอดมินเพื่อทำการปลดล็อค`,
                  confirmButtonText: "Close",
                }).then((result) => {
                  $("#makeWithdrawPinAlertText").text(
                    `เหลือสิทธิ์ในการกรอก pin code อีก 0 ครั้ง`
                  );
                  window.location.reload();
                });
              } else if (res.error == "4") {
                $("#makeWithdrawPinCode").val("");
                $("#makeWithdrawToken").val("");

                Swal.fire({
                  icon: "warning",
                  title: `คุณกรอก Pin Code ผิดเกินจำนวนครั้งที่กำหนด. Pin code lock กรุณาติดต่อแอดมินเพื่อทำการปลดล็อค`,
                  confirmButtonText: "Close",
                }).then((result) => {
                  $("#makeWithdrawPinAlertText").text(
                    `เหลือสิทธิ์ในการกรอก pin code อีก 0 ครั้ง`
                  );
                  window.location.reload();
                });
              } else {
                $("#makeWithdrawPinCode").val("");
                $("#makeWithdrawToken").val("");

                $("#makeWithdrawOrderId").removeAttr("disabled");
                $("#makeWithdrawAmount").removeAttr("disabled");
                $("#makeWithdrawBankAccountName").removeAttr("disabled");
                $("#makeWithdrawBankAccountNo").removeAttr("disabled");
                $("#makeWithdrawBankName").removeAttr("disabled");
                let message = res.message;
                if (res.result.msg != undefined) {
                  message = res.result.msg;
                }
                Swal.fire({
                  icon: "warning",
                  title: message,
                  confirmButtonText: "Close",
                }).then((result) => {});
              }
            },
          });
        }

        return false;
      }

      function exportData() {
        Swal.fire({
          title: "Processing data to export",
          allowOutsideClick: false,
          showConfirmButton: false,
          onBeforeOpen: () => {
            Swal.showLoading();
          },
        });
        Swal.showLoading();

        setTimeout(function () {
          var text_search = $("#text_search").val().trim();
          var status_search = $("#status_search").val().trim();
          var channel_search = $("#channel_search").val().trim();
          var transaction_type_search = $("#transaction_type_search")
            .val()
            .trim();
          var deposit_type_search = $("#deposit_type_search").val().trim();

          let check = true;
          let page = 1;
          let length = 3000;
          let result_csv = [];
          while (check) {
            $.ajax({
              url: url_ajax + "getDatatableList",
              method: "POST",
              data: {
                text_search: text_search,
                status_search: status_search,
                channel_search: channel_search,
                transaction_type_search: transaction_type_search,
                deposit_type_search: deposit_type_search,
                start_date: start_date + ":00",
                end_date: end_date + ":59",
                start: (page - 1) * length,
                length: length,
                draw: page,
              },
              async: false,
              dataType: "json",
              success: function (response) {
                if (response.error == 0) {
                  result_csv = result_csv.concat(response.result);
                } else {
                  // console.log("step 3");
                  check = false;
                }
              },
            });
            if (page > 50) {
              check = false;
            }
            page++;
          }
          if (result_csv.length > 0) {
            exportExcel(
              result_csv,
              `transaction_${moment()
                .format("YYYY-MM-DD_HH-mm-ss")
                .toString()}.xlsx`
            );
            // export_csv(result_csv,"export_รายชื่อพระภิกษุ_"+moment().format("YYYY-MM-DD_HH-mm-ss").toString())
          } else {
            alert("ไม่พบข้อมูล");
          }
          Swal.close();
        }, 300);

        return false;
      }

      function exportExcel(data, filename) {
        // Define the data to be exported
        // const data = [
        //     { name: 'Alice', age: 25, email: '<EMAIL>' },
        //     { name: 'Bob', age: 30, email: '<EMAIL>' },
        //     { name: 'Charlie', age: 35, email: '<EMAIL>' }
        // ];

        // Define the filename for the exported file
        // const filename = 'example.xlsx';

        // Convert the data to a worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(data);

        // define style with table border
        worksheet["!table"] = {
          // Add the border style
          style: "border: 1px solid black;",
        };

        // Create a new workbook and add the worksheet to it
        XLSX.utils.book_append_sheet(workbook, worksheet);

        // Convert the workbook to a binary string
        const binaryString = XLSX.write(workbook, {
          bookType: "xlsx",
          type: "binary",
        });

        // Convert the binary string to a Blob object
        const blob = new Blob([s2ab(binaryString)], {
          type: "application/octet-stream",
        });

        // Save the Blob object as a file using FileSaver.js
        window.saveAs(blob, filename);
      }

      // Helper function to convert a string to an ArrayBuffer
      function s2ab(s) {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i < s.length; i++) {
          view[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
      }

      function confirmTransaction(statement_id) {
        if (statement_id != "") {
          $.ajax({
            url: url_ajax + "statement_detail",
            type: "post",
            async: false,
            data: {
              statement_id: statement_id,
            },
            dataType: "json",
            success: function (res) {
              $("#formConfirmTransaction")[0].reset();
              if (res.error == 0) {
                $("#confirmTransactionStatementID").val(
                  res.result.statement_id
                );
                $("#confirmTransactionTransactionDate").val(
                  moment(res.result.txn_date).format("YYYY-MM-DD HH:mm:ss")
                );
                $("#confirmTransactionOrderID").val(res.result.order_id);
                $("#confirmTransactionFromAccount").val(
                  `${res.result.txn_bank_name} ${res.result.txn_acc_4last}`
                );
                $("#confirmTransactionAmount").val(
                  formatNumber(res.result.txn_amount, 2)
                );
                $("#confirmTransactionRemark").val(res.result.txn_remark);

                $("#modalConfirmStatement").modal("show");
              } else {
                Swal.fire({
                  icon: "warning",
                  title: `Somethings error. data not found.`,
                  confirmButtonText: "ปิด",
                });
              }
            },
          });
        }

        return false;
      }

      function reportWithdrawProblem(transaction_id) {
        if (transaction_id != "") {
          $.ajax({
            url: url_ajax + "transaction_detail",
            type: "post",
            async: false,
            data: {
              transaction_id: transaction_id,
            },
            dataType: "json",
            success: function (res) {
              $("#formReportWithdrawProblem")[0].reset();
              if (res.error == 0) {
                $("#reportWithdrawProblemTransactionID").val(
                  res.result.transaction_id
                );
                $("#reportWithdrawProblemTransactionDate").val(
                  moment(res.result.created_date).format("YYYY-MM-DD HH:mm:ss")
                );
                $("#reportWithdrawProblemOrderID").val(res.result.order_id);
                $("#reportWithdrawProblemToAccount").val(
                  `${res.result.withdraw_bank_name} ${res.result.withdraw_acc_no}`
                );
                $("#reportWithdrawProblemAmount").val(
                  formatNumber(res.result.amount, 2)
                );

                $("#modalReportWithdrawProblem").modal("show");
              } else {
                Swal.fire({
                  icon: "warning",
                  title: `Somethings error. data not found.`,
                  confirmButtonText: "ปิด",
                });
              }
            },
          });
        }

        return false;
      }

      function confirmReportWithdrawProblem() {
        Swal.fire({
          title: "แจ้งปัญหา",
          text: "คุณยืนยันการแจ้งปัญหารายการถอนเงินนี้ใช่หรือไหม?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "ยืนยัน",
          cancelButtonText: "ยกเลิก",
        }).then((result) => {
          if (result.isConfirmed) {
            saveReportWithdrawProblem();
          }
        });
      }

      function saveReportWithdrawProblem() {
        let transaction_id = $("#reportWithdrawProblemTransactionID").val();
        let withdraw_problem_type = $("#reportWithdrawProblemType").val();
        let withdraw_problem_remark = $("#reportWithdrawProblemRemark").val();
        $.ajax({
          url: url_ajax + `save_report_withdraw_problem`,
          type: "post",
          async: false,
          data: {
            transaction_id: transaction_id,
            withdraw_problem_type: withdraw_problem_type,
            withdraw_problem_remark: withdraw_problem_remark,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == 0) {
              Swal.fire({
                icon: "success",
                title: `Sent report success. waiting admin recheck data.`,
                confirmButtonText: "Close",
              }).then((result) => {
                $("#modalReportWithdrawProblem").modal("hide");
                get_datatable_list();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Warning",
                text: "Transaction data not found",
                showConfirmButton: true,
                confirmButtonText: "Ok",
              });
            }
          },
        });

        return false;
      }

      function confirmConfirmTransaction() {
        Swal.fire({
          title: "Confirm",
          text: "Are you want to confirm this transaction?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#0ab39c",
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveConfirmTransaction("Y");
          }
        });
      }

      function confirmResendCallback(transaction_id) {
        Swal.fire({
          title: "Confirm",
          text: "Are you want to resent this transaction callback? ",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#0ab39c",
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveResendCallback(transaction_id);
          }
        });
      }

      function saveResendCallback(transaction_id) {
        $.ajax({
          url: `/api/resend_callback`,
          type: "post",
          async: false,
          data: {
            transaction_id: transaction_id,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == 0) {
              Swal.fire({
                icon: "success",
                title: `Resent callback success`,
                confirmButtonText: "Close",
              }).then((result) => {});
            } else {
              Swal.fire({
                icon: "warning",
                title: "Warning",
                text: "Transaction data not found",
                showConfirmButton: true,
                confirmButtonText: "Ok",
              });
            }
          },
        });

        return false;
      }

      function refuseConfirmTransaction() {
        Swal.fire({
          title: "Refuse",
          text: "Are you want to refuse this transaction?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#cc563d",
          confirmButtonText: "Refuse",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveConfirmTransaction("N");
          }
        });
      }

      function saveConfirmTransaction(is_agent_confirm) {
        let statement_id = $("#confirmTransactionStatementID").val();
        $.ajax({
          url: url_ajax + "confirm_transaction",
          type: "post",
          async: false,
          data: {
            statement_id: statement_id,
            is_agent_confirm: is_agent_confirm,
          },
          dataType: "json",
          success: function (res) {
            console.log(res);
            if (res.error == 0) {
              Swal.fire({
                icon: "success",
                title: `Update success`,
                confirmButtonText: "Close",
              }).then((result) => {
                $("#modalConfirmStatement").modal("hide");
                get_datatable_list();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Warning",
                text: "Somethings error. can't update data. please try again",
                showConfirmButton: true,
                confirmButtonText: "Ok",
              });
            }
          },
        });

        return false;
      }

      async function showModalSlip(instructionRefNo) {
        $.blockUI({
          css: { backgroundColor: "#fff", color: "#000", borderColor: "#fff" },
          message: "กำลังโหลดข้อมูล",
        });
        var formdata = new FormData();
        formdata.append("instructionRefNo", instructionRefNo);
        var requestOptions = {
          method: "POST",
          body: formdata,
          redirect: "follow",
        };
        var res = await fetch(
          url_ajax + "getWithdrawSlip",
          requestOptions
        ).then((response) => response.json());

        if (res.error == 0) {
          $("#modalSlip").modal("show");

          if (res.result.from == "EMAIL") {
            $("#htmlEmail").html(res.result.data.message);
          } else {
            $("#htmlEmail").text(res.result.data.message);
          }
          try {
            $("#dateEmail").text(
              ` วันที่ ${moment(res.result.data.created_date).format(
                "DD/MM/YYYY HH:mm"
              )}`
            );
          } catch (e) {}

          $.unblockUI();
        } else {
          $.unblockUI();
          alert("รอข้อมูลยืนยันจากธนาคารฯ");
        }

        return res;
      }

      $("#makePaymentOrderId,#makeWithdrawOrderId").on(
        "keypress keyup",
        function (e) {
          var regex = /^[A-Za-z0-9_\-]+$/;
          var inputValue = $(this).val();

          if (!regex.test(inputValue)) {
            // Remove invalid characters
            $(this).val(inputValue.replace(/[^A-Za-z0-9_\-]/g, ""));
          }
        }
      );
      function confirmCancelWithdraw(transaction_id) {
        Swal.fire({
          title: "Confirm",
          text: "Are you want to cancel this withdraw transaction ? ",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#b30a0a",
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveCancelWithdraw(transaction_id);
          }
        });
      }

      function saveCancelWithdraw(transaction_id) {
        $.ajax({
          url: url_ajax + "cancel_withdraw",
          type: "post",
          async: false,
          data: {
            transaction_id: transaction_id,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == 0) {
              Swal.fire({
                icon: "success",
                title: `Cancel withdraw success`,
                confirmButtonText: "Close",
              }).then((result) => {
                get_datatable_list();
              });
            } else if (res.error == 2) {
              Swal.fire({
                icon: "warning",
                title: `This withdraw is on bank process`,
                confirmButtonText: "Close",
              }).then((result) => {
                get_datatable_list();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Warning",
                text: "Transaction data not found",
                showConfirmButton: true,
                confirmButtonText: "Ok",
              });
            }
          },
        });

        return false;
      }
    </script>
  </body>
</html>
